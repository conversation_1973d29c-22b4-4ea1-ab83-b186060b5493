'use client';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { generateRandomSixCharCode } from '@/lib/utils';
import { createProject } from '@/lib/features/projects/projectsSlice';

const createProjectsSchema = z.object({
	code: z
		.string()
		.nonempty('Project code is required')
		.max(6, 'Project code must be at most 6 characters long'),
	name: z
		.string()
		.nonempty('Project name is required')
		.max(50, 'Project name must be at most 50 characters long'),
	description: z.string().optional(),
	status: z.enum(['active', 'inactive']).default('active'),
	lead: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Project lead is required'),
});

export function AddProjectDialog({ open, onOpenChange }) {
	const { employees, isLoading } = useAppSelector((store) => store.projects);
	const dispatch = useAppDispatch();

	const form = useForm({
		mode: 'onChange',

		resolver: zodResolver(createProjectsSchema),
		defaultValues: {
			code: '',
			name: '',
			description: '',
			status: 'active',
			lead: '',
		},
	});

	useEffect(() => {
		const code = generateRandomSixCharCode();
		form.setValue('code', code, { shouldDirty: true });
	}, [form, open]);

	const onSubmit = async (data) => {
		await dispatch(createProject(data));
		onOpenChange(false);
		form.reset();
	};

	const handleOpenChange = (newOpen) => {
		if (!isLoading) {
			onOpenChange(newOpen);
			if (!newOpen) {
				form.reset();
			}
		}
	};

	return (
		<Dialog open={open} onOpenChange={handleOpenChange}>
			<DialogContent className="sm:max-w-[425px]">
				<DialogHeader>
					<DialogTitle>Add New Project</DialogTitle>
					<DialogDescription>
						Create a new project by filling out the form below.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
						<FormField
							control={form.control}
							name="code"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Project Code</FormLabel>
									<FormControl>
										<Input
											placeholder="Enter project code"
											{...field}
											maxLength={6}
										/>
									</FormControl>
									<FormDescription>
										This ID is randomly generated but can be edited once
										according to your preference.
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Project Name</FormLabel>
									<FormControl>
										<Input
											placeholder="Enter project name"
											{...field}
											maxLength={50}
										/>
									</FormControl>
									<FormDescription>Maximum 50 characters</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="description"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Description</FormLabel>
									<FormControl>
										<Textarea
											placeholder="Enter project description (optional)"
											className="resize-none"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="status"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Status</FormLabel>
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select project status" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											<SelectItem value="active">Active</SelectItem>
											<SelectItem value="inactive">Inactive</SelectItem>
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="lead"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Project Lead</FormLabel>
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select a project lead" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											{employees.map((employee) => (
												<SelectItem
													key={employee.reportingUserId}
													value={employee.reportingUserId}
												>
													{employee.reportingUserName}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
									<FormDescription>
										Assign a project lead to this project
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<DialogFooter>
							<Button
								type="button"
								variant="outline"
								onClick={() => handleOpenChange(false)}
								disabled={isLoading}
							>
								Cancel
							</Button>
							<Button type="submit" disabled={isLoading}>
								{isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
								{isLoading ? 'Creating...' : 'Create Project'}
							</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
