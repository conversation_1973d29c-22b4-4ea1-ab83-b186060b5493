import { customFetch, showErrors } from '@/lib/utils';
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { toast } from 'sonner';

const initialState = {
	chats: [],
	chatUsers: [],
	selectedChat: null,
	messages: [],
	happenings: [],
	isLoading: false,
	error: null,
	selectedChatUser: null,
};

export const getChatUsers = createAsyncThunk(
	'chat/getChatUsers',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/chat/users');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const getChats = createAsyncThunk(
	'chat/getChats',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/chat');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const getMessagesForHappenings = createAsyncThunk(
	'chat/getMessagesForHappenings',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/message/happenings');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const createChat = createAsyncThunk(
	'chat/createChat',
	async (chatDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post('/chat', chatDetails);

			if (data?.success) {
				thunkAPI.dispatch(getChats());
			}
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const sendMessage = createAsyncThunk(
	'chat/sendMessage',
	async (messageDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post('/message', messageDetails);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const getMessages = createAsyncThunk(
	'chat/getMessages',
	async (chatId, thunkAPI) => {
		try {
			const { data } = await customFetch.get(`/message/${chatId}`);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const markChatAsRead = createAsyncThunk(
	'chat/markChatAsRead',
	async (messageId, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(`/message/${messageId}`);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data.message);
		}
	}
);

const chatSlice = createSlice({
	name: 'chat',
	initialState,
	reducers: {
		setSelectedChat: (state, action) => {
			state.selectedChat = action.payload;
			state.selectedChatUser = state.chats.find(
				(c) => c._id === action.payload
			);
		},
		receiveMessage: (state, action) => {
			if (state.selectedChat === action.payload.chat) {
				state.messages.push(action.payload);
			}
		},
		markMessageAsRead: (state, action) => {
			// const message = state.messages.find((m) => m._id === action.payload);
			// if (message) {
			// 	message.isRead = true;
			// }
			state.messages = state.messages.map((m) =>
				m._id === action.payload ? { ...m, isRead: true } : m
			);
		},
	},
	extraReducers: (builder) => {
		builder
			.addCase(getChats.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(getChats.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.chats = payload.data;
			})
			.addCase(getChats.rejected, (state, action) => {
				state.isLoading = false;
				showErrors(payload, toast);
			})
			.addCase(createChat.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(createChat.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				// toast.success(payload.message);
			})
			.addCase(createChat.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload, toast);
			})
			.addCase(sendMessage.fulfilled, (state, { payload }) => {
				state.sentMessage = payload.data.message;
				// state.messages.push(payload.data.message);
				// console.log(` .addCase - state.sentMessage:`, state.sentMessage);
			})
			.addCase(sendMessage.rejected, (state, { payload }) => {
				showErrors(payload, toast);
			})
			.addCase(getMessages.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(getMessages.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.messages = payload.data;
				// console.log(` .addCase - state.messages:`, state.messages);
			})
			.addCase(getMessages.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload, toast);
			})
			.addCase(getChatUsers.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(getChatUsers.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.chatUsers = payload.data;
			})
			.addCase(getChatUsers.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload, toast);
			})
			.addCase(getMessagesForHappenings.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(getMessagesForHappenings.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.happenings = payload.data;
				// console.log(` .addCase - state.messages:`, state.messages);
			})
			.addCase(getMessagesForHappenings.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload, toast);
			});
	},
});

export const { setSelectedChat, receiveMessage, markMessageAsRead } =
	chatSlice.actions;
export default chatSlice.reducer;
