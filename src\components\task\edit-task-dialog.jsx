'use client';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver as rhfResolver } from '@hookform/resolvers/zod';
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogDescription,
	DialogFooter,
	DialogClose,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { CalendarIcon } from 'lucide-react';
import ColorPickerInput from './color-picker-input'; // New import
import { formatDate } from '@/lib/utils';

// Zod schema based on user input for updating task
const updateTaskSchema = z.object({
	name: z.string().optional(),
	description: z.string().optional(),
	assignedTo: z.string().optional(), // Assuming ID string
	dueDate: z.date().optional(),
	priority: z.enum(['low', 'medium', 'high']).optional(),
	color: z
		.string()
		.regex(/^([A-Fa-f0-9]{6})$/, 'Color must be a valid 6-digit hex color code')
		.optional(),
	// Note: status is handled separately in TaskDetailsDialog for this example
	// Note: media updates are complex and not included in this simplified edit form.
	// You might want a separate interface for managing media or add it here.
});

export default function EditTaskDialog({
	isOpen,
	onClose,
	task,
	projects,
	users,
	onTaskUpdated,
}) {
	const form = useForm({
		resolver: rhfResolver(updateTaskSchema),
		mode: 'onChange',

		defaultValues: {
			name: task?.name || '',
			description: task?.description || '',
			assignedTo: task?.assignedTo?.$oid || task?.assignedTo || '',
			dueDate: task?.dueDate?.$date
				? new Date(task.dueDate.$date)
				: task?.dueDate
					? new Date(task.dueDate)
					: undefined,
			priority: task?.priority || 'medium',
			color: task?.color || 'FFFFFF',
		},
	});

	useEffect(() => {
		if (task) {
			form.reset({
				name: task.name || '',
				description: task.description || '',
				assignedTo: task.assignedTo?.$oid || task.assignedTo || '',
				dueDate: task.dueDate?.$date
					? new Date(task.dueDate.$date)
					: task.dueDate
						? new Date(task.dueDate)
						: undefined,
				priority: task.priority || 'medium',
				color: task.color || 'FFFFFF',
			});
		}
	}, [task, form]);

	const onSubmit = (data) => {
		const updatedData = {
			_id: task._id.$oid, // Ensure task ID is included
			...data,
			dueDate: data.dueDate ? data.dueDate.toISOString().split('T')[0] : null,
		};
		onTaskUpdated(updatedData);
		onClose();
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="sm:max-w-lg md:max-w-xl max-h-[90vh] flex flex-col">
				<DialogHeader>
					<DialogTitle>Edit Task: {task?.name}</DialogTitle>
					<DialogDescription>Update the task details below.</DialogDescription>
				</DialogHeader>
				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className="space-y-4 flex-grow overflow-y-auto pr-2 py-4"
					>
						<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Task Name</FormLabel>
									<FormControl>
										<Input placeholder="Enter task name" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="description"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Description (Optional)</FormLabel>
									<FormControl>
										<Textarea placeholder="Enter task description" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="priority"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Priority</FormLabel>
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select priority" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											{['low', 'medium', 'high'].map((p) => (
												<SelectItem key={p} value={p} className="capitalize">
													{p}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="assignedTo"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Assign To (Optional)</FormLabel>
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select user" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											<SelectItem value="unassigned">
												<em>Unassigned</em>
											</SelectItem>
											{users.map((u) => (
												<SelectItem key={u.id} value={u.id}>
													{u.name}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="dueDate"
							render={({ field }) => (
								<FormItem className="flex flex-col">
									<FormLabel>Due Date</FormLabel>
									<Popover>
										<PopoverTrigger asChild>
											<FormControl>
												<Button
													variant={'outline'}
													className={`w-full pl-3 text-left font-normal ${!field.value && 'text-muted-foreground'}`}
												>
													{field.value ? (
														formatDate(field.value)
													) : (
														<span>Pick a date</span>
													)}
													<CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
												</Button>
											</FormControl>
										</PopoverTrigger>
										<PopoverContent className="w-auto p-0" align="start">
											<Calendar
												mode="single"
												selected={field.value}
												onSelect={field.onChange}
												initialFocus
											/>
										</PopoverContent>
									</Popover>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="color"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Color Tag</FormLabel>
									<ColorPickerInput
										value={field.value}
										onChange={field.onChange}
									/>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Add other fields like project, color if they are editable */}
					</form>
				</Form>
				<DialogFooter className="pt-4 border-t">
					<DialogClose asChild>
						<Button variant="ghost">Cancel</Button>
					</DialogClose>
					<Button
						type="submit"
						onClick={form.handleSubmit(onSubmit)}
						disabled={form.formState.isSubmitting}
					>
						{form.formState.isSubmitting ? 'Saving...' : 'Save Changes'}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
