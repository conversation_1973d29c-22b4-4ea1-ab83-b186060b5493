'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useFieldArray, useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
	Check,
	ChevronsUpDown,
	Loader2,
	PlusCircle,
	Trash2,
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { contactDetailsFormSchema } from '@/lib/schemas/employeeRegistrationSchema';
import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { updateEmployeeContactDetails } from '@/lib/features/employees/employeeSlice';
import { registerOnboardingLinkContactDetails } from '@/lib/features/employees/employeeOnboardingSlice';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from './ui/command';
import { cn } from '@/lib/utils';
import { fetchDialCodes } from '@/lib/features/location/locationSlice';
import { LoadingSubmitButton } from './loading-component';

export function EmployeeContactDetailsForm() {
	const dispatch = useAppDispatch();
	const { employeeDetails, isLoading } = useAppSelector(
		(store) => store.employee
	);
	const { onBoardingEmployeeDetails } = useAppSelector(
		(store) => store.employeeOnboarding
	);
	const { isLoading: isLoadingDialCodes, dialCodes } = useAppSelector(
		(store) => store.location
	);
	const form = useForm({
		resolver: zodResolver(contactDetailsFormSchema),
		mode: 'onChange',

		defaultValues: {
			// employeeOrgId: '',
			contacts: [
				{
					type: 'emergency',
					name: '',
					relationship: '',
					countryDialCode: '+1',
					phone: '',
					email: '',
				},
				// {
				// 	type: 'reference',
				// 	name: '',
				// 	relationship: '',
				// 	countryDialCode: '+1',
				// 	phone: '',
				// 	email: '',
				// },
			],
		},
	});

	const {
		fields: contactFields,
		append: appendContact,
		remove: removeContact,
	} = useFieldArray({
		name: 'contacts',
		control: form.control,
	});

	function onSubmit(data) {
		if (onBoardingEmployeeDetails?.email) {
			dispatch(
				registerOnboardingLinkContactDetails({
					...data,
					employeeId: onBoardingEmployeeDetails?._id,
				})
			);
		} else {
			dispatch(
				updateEmployeeContactDetails({
					...data,
					employeeId: employeeDetails?.employeeId || employeeDetails?._id,
				})
			);
		}
	}

	useEffect(() => {
		dispatch(fetchDialCodes());
	}, [dispatch]);

	useEffect(() => {
		console.log(form.formState.errors);
	}, [form.formState.errors]);

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
				{/* <FormField
					control={form.control}
					name="employeeOrgId"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Employee ID</FormLabel>
							<FormControl>
								<Input placeholder="Enter employee ID" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/> */}

				<div className="space-y-4">
					<h3 className="text-lg font-medium">Contact Details</h3>
					<Separator />
					{contactFields.map((field, index) => (
						<Card key={field.id} className="relative">
							<CardContent className="pt-6">
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<FormField
										control={form.control}
										name={`contacts.${index}.type`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Contact Type</FormLabel>
												<Select
													onValueChange={field.onChange}
													defaultValue={field.value}
												>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder="Select contact type" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="emergency">Emergency</SelectItem>
														<SelectItem value="reference">Reference</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`contacts.${index}.name`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Name</FormLabel>
												<FormControl>
													<Input placeholder="Enter name" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`contacts.${index}.relationship`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Relationship</FormLabel>
												<FormControl>
													<Input placeholder="Enter relationship" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<div className="flex space-x-2">
										<FormField
											control={form.control}
											name={`contacts.${index}.countryDialCode`}
											render={({ field }) => (
												<FormItem className="col-span-1">
													<FormLabel>Dial Code</FormLabel>
													<Popover>
														<PopoverTrigger asChild>
															<FormControl>
																<Button
																	variant="outline"
																	role="combobox"
																	className={cn(
																		'w-full justify-between',
																		!field.value && 'text-muted-foreground'
																	)}
																>
																	{field.value
																		? dialCodes.find(
																				(code) => code === field.value
																			)
																		: 'Code'}
																	{isLoadingDialCodes && (
																		<Loader2 className="h-4 w-4 animate-spin" />
																	)}
																	{!isLoadingDialCodes && (
																		<ChevronsUpDown className="opacity-50" />
																	)}
																</Button>
															</FormControl>
														</PopoverTrigger>
														<PopoverContent className="w-full p-0">
															<Command>
																<CommandInput
																	placeholder="Search Dial Code..."
																	className="h-9 w-full"
																/>
																<CommandList className="w-full">
																	<CommandEmpty>
																		No Dial Code found.
																	</CommandEmpty>
																	<CommandGroup>
																		{dialCodes.map((code) => (
																			<CommandItem
																				value={code}
																				key={code}
																				onSelect={() => {
																					form.setValue(
																						`contacts.${index}.countryDialCode`,
																						code
																					);
																				}}
																			>
																				{code}
																				<Check
																					className={cn(
																						'ml-auto',
																						code === field.value
																							? 'opacity-100'
																							: 'opacity-0'
																					)}
																				/>
																			</CommandItem>
																		))}
																	</CommandGroup>
																</CommandList>
															</Command>
														</PopoverContent>
													</Popover>
													<FormMessage />
												</FormItem>
											)}
										/>
										<FormField
											control={form.control}
											name={`contacts.${index}.phone`}
											render={({ field }) => (
												<FormItem className="flex-1">
													<FormLabel>Phone Number</FormLabel>
													<FormControl>
														<Input
															placeholder="Enter phone number"
															{...field}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>
									<FormField
										control={form.control}
										name={`contacts.${index}.email`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Email</FormLabel>
												<FormControl>
													<Input placeholder="Enter email" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
								{index >= 1 && (
									<Button
										type="button"
										variant="ghost"
										size="icon"
										onClick={() => removeContact(index)}
										className="absolute top-2 right-2"
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								)}
							</CardContent>
						</Card>
					))}
					<Button
						type="button"
						variant="outline"
						size="sm"
						onClick={() =>
							appendContact({
								type: 'reference',
								name: '',
								relationship: '',
								countryCode: '+1',
								phone: '',
								email: '',
							})
						}
					>
						<PlusCircle className="mr-2 h-4 w-4" />
						Add More Contact
					</Button>
				</div>

				{/* <div className="space-y-4">
					<h3 className="text-lg font-medium">Family Details</h3>
					<Separator />
					<FormField
						control={form.control}
						name="maritalStatus"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Marital Status</FormLabel>
								<Select
									onValueChange={field.onChange}
									defaultValue={field.value}
								>
									<FormControl>
										<SelectTrigger>
											<SelectValue placeholder="Select marital status" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value="single">Single</SelectItem>
										<SelectItem value="married">Married</SelectItem>
										<SelectItem value="other">Other</SelectItem>
										<SelectItem value="prefer_not_to_say">
											Prefer not to say
										</SelectItem>
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>

					{watchMaritalStatus === 'married' && (
						<>
							<FormField
								control={form.control}
								name="spouseName"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Spouse Name</FormLabel>
										<FormControl>
											<Input placeholder="Enter spouse name" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="spouseEmploymentStatus"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Spouse Employment Status</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select employment status" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												<SelectItem value="employed">Employed</SelectItem>
												<SelectItem value="unemployed">Unemployed</SelectItem>
												<SelectItem value="prefer_not_to_say">
													Prefer not to say
												</SelectItem>
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
						</>
					)}

					<div className="space-y-4">
						<h4 className="text-md font-medium">Children</h4>
						{childrenFields.map((field, index) => (
							<Card key={field.id} className="relative">
								<CardContent className="pt-6">
									<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
										<FormField
											control={form.control}
											name={`children.${index}.relationship`}
											render={({ field }) => (
												<FormItem>
													<FormLabel>Relationship</FormLabel>
													<FormControl>
														<Input
															placeholder="Enter relationship"
															{...field}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
										<FormField
											control={form.control}
											name={`children.${index}.name`}
											render={({ field }) => (
												<FormItem>
													<FormLabel>Name</FormLabel>
													<FormControl>
														<Input
															placeholder="Enter child's name"
															{...field}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
										<FormField
											control={form.control}
											name={`children.${index}.dob`}
											render={({ field }) => (
												<FormItem>
													<FormLabel>Date of Birth</FormLabel>
													<FormControl>
														<Input type="date" {...field} />
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>
									<Button
										type="button"
										variant="ghost"
										size="icon"
										onClick={() => removeChild(index)}
										className="absolute top-2 right-2"
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								</CardContent>
							</Card>
						))}
						<Button
							type="button"
							variant="outline"
							size="sm"
							onClick={() =>
								appendChild({
									relationship: '',
									name: '',
									dob: '',
								})
							}
						>
							<PlusCircle className="mr-2 h-4 w-4" />
							Add Child
						</Button>
					</div>
				</div> */}

				<div className="flex items-end space-x-4">
					<LoadingSubmitButton
						isLoading={isLoading}
						buttonText={'Save and Next'}
						buttonLoadingText={'Saving...'}
					/>
				</div>
			</form>
		</Form>
	);
}
