'use client';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from './ui/form';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Button } from './ui/button';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { cn } from '@/lib/utils';
import { Check, ChevronsUpDown, Loader2 } from 'lucide-react';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from './ui/command';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { LoadingSubmitButton } from './loading-component';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from './ui/select';
import {
	fetchCurrencies,
	fetchCountries,
} from '@/lib/features/location/locationSlice';
import { createCompanySchema } from '@/lib/schemas/gClientAdminSchema';
import ProfilePhotoUpload from './ui/profile-photo-uploader';
import { addCompany } from '@/lib/features/glorified-client-admin/glorifiedClientAdminSlice';
import { countryDataFormats } from '@/lib/utils';

export function CreateCompany({ onSuccess }) {
	const [selectedFile, setSelectedFile] = useState(null);
	const [registrationFormat, setRegistrationFormat] = useState(null);

	const dispatch = useAppDispatch();
	const { isLoading, countries, currencies } = useAppSelector(
		(store) => store.location
	);
	const { isLoading: isLoadingCompany } = useAppSelector(
		(store) => store.glorifiedClientAdmin
	);

	// Initialize form with dynamic schema
	const form = useForm({
		mode: 'onChange',

		resolver: zodResolver(createCompanySchema(registrationFormat?.regex)),
		defaultValues: {
			businessName: '',
			businessCountry: '',
			businessCountryDialCode: '',
			registration: '',
			address: '',
			currency: '',
			timeFormat: '',
			dateFormat: '',
		},
	});

	// Update resolver when registrationFormat changes
	useEffect(() => {
		form.reset(form.getValues()); // Reset form to trigger revalidation
		form.clearErrors(); // Clear previous errors
	}, [registrationFormat, form]);

	useEffect(() => {
		dispatch(fetchCountries());
		dispatch(fetchCurrencies());
	}, [dispatch]);

	// For debugging form data
	// useEffect(() => {
	// 	const subscription = form.watch((value) => {
	// 		console.log(value);
	// 	});
	// 	return () => subscription.unsubscribe();
	// }, [form.watch, dispatch, form]);

	const onSubmit = async (data) => {
		const formData = new FormData();
		Object.entries(data).forEach(([key, value]) => {
			if (key === 'businessCountryDialCode') {
				return;
			}
			formData.append(key, value);
		});
		if (selectedFile) {
			formData.append('logo', selectedFile);
		}

		const result = await dispatch(addCompany(formData)).unwrap();

		if (result && onSuccess) {
			onSuccess();
		}
	};

	const handleImageChange = (file) => {
		setSelectedFile(file);
		console.log('File selected:', file?.name, file);
	};

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-4">
				<section className="grid grid-cols-12 gap-2">
					<div className="flex flex-col-reverse lg:flex-row gap-4 items-center justify-between lg:items-end col-span-12">
						<article className="grid md:grid-cols-2 lg:grid-cols-3 gap-2 flex-1 w-full">
							{/* Business Name */}
							<FormField
								control={form.control}
								name="businessName"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Business Name</FormLabel>
										<FormControl>
											<Input placeholder="Business Name" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Country Selection with Virtualized Dropdown */}

							<FormField
								control={form.control}
								name="businessCountry"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Country</FormLabel>
										<Popover>
											<PopoverTrigger asChild>
												<FormControl>
													<Button
														variant="outline"
														role="combobox"
														className={cn(
															'w-full justify-between',
															!field.value && 'text-muted-foreground'
														)}
													>
														{field.value
															? countries.find(
																	(country) => country._id === field.value
																)?.name
															: 'Select country'}
														{isLoading && (
															<Loader2 className="h-4 w-4 animate-spin" />
														)}
														{!isLoading && (
															<ChevronsUpDown className="opacity-50" />
														)}
													</Button>
												</FormControl>
											</PopoverTrigger>
											<PopoverContent className="w-full p-0">
												<Command>
													<CommandInput
														placeholder="Search Country..."
														className="h-9"
													/>
													<CommandList>
														<CommandEmpty>No Country found.</CommandEmpty>
														<CommandGroup>
															{countries.map((country) => (
																<CommandItem
																	value={country.name}
																	key={country._id}
																	onSelect={() => {
																		form.setValue(
																			'businessCountry',
																			country._id
																		);
																		form.setValue(
																			'businessCountryDialCode',
																			`+${country.phoneCode}`
																		);
																		form.setValue(
																			'currency',
																			`${country.currency}`
																		);

																		const selectedCountryName = country.name;
																		const matchedFormat =
																			countryDataFormats.find(
																				(c) =>
																					c.country.toLowerCase() ===
																					selectedCountryName.toLowerCase()
																			);

																		if (matchedFormat?.registration) {
																			setRegistrationFormat({
																				label: matchedFormat.registration.label,
																				description:
																					matchedFormat.registration
																						.description,
																				regex: matchedFormat.registration.regex,
																			});
																		} else {
																			setRegistrationFormat(null);
																		}
																	}}
																>
																	{country.name}
																	<Check
																		className={cn(
																			'ml-auto',
																			country._id === field.value
																				? 'opacity-100'
																				: 'opacity-0'
																		)}
																	/>
																</CommandItem>
															))}
														</CommandGroup>
													</CommandList>
												</Command>
											</PopoverContent>
										</Popover>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Country Code */}
							<FormField
								control={form.control}
								name="businessCountryDialCode"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Country Dial Code</FormLabel>
										<FormControl>
											<Input
												placeholder="Country Dial Code"
												{...field}
												disabled
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</article>
						<section className="flex flex-col items-center w-full lg:w-auto">
							<ProfilePhotoUpload onImageChange={handleImageChange} size="lg" />
							<div className="text-sm text-muted-foreground text-center mt-2">
								{selectedFile ? (
									<p>Selected: {selectedFile.name}</p>
								) : (
									<p>Click the circle to upload organization Logo</p>
								)}
							</div>
						</section>
					</div>
					{/* Registration */}
					<FormField
						control={form.control}
						name="registration"
						render={({ field }) => (
							<FormItem className="col-span-12 md:col-span-4 lg:col-span-3">
								<FormLabel>
									{registrationFormat?.label || 'Registration Number'}
								</FormLabel>
								<FormControl>
									<Input
										key={registrationFormat?.label || 'default'}
										placeholder={
											registrationFormat?.label || 'Registration Number'
										}
										{...field}
									/>
								</FormControl>
								{registrationFormat?.description && (
									<FormDescription>
										{registrationFormat.description}
									</FormDescription>
								)}
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Currency, Time Format, Date Format, Address Fields */}
					<FormField
						control={form.control}
						name="currency"
						render={({ field }) => (
							<FormItem className="col-span-12 md:col-span-4 lg:col-span-3">
								<FormLabel>Currency</FormLabel>
								<Popover>
									<PopoverTrigger asChild>
										<FormControl>
											<Button
												variant="outline"
												role="combobox"
												className={cn(
													'w-full justify-between',
													!field.value && 'text-muted-foreground'
												)}
											>
												{field.value
													? currencies?.find(
															(currency) => currency.currency === field.value
														)?.currency
													: 'Select currency'}
												<ChevronsUpDown className="opacity-50" />
											</Button>
										</FormControl>
									</PopoverTrigger>
									<PopoverContent className="w-full p-0">
										<Command>
											<CommandInput
												placeholder="Search Currency..."
												className="h-9"
											/>
											<CommandList>
												<CommandEmpty>No Currency found.</CommandEmpty>
												<CommandGroup>
													{currencies?.map((currency, index) => (
														<CommandItem
															value={currency.currency}
															key={index + 5}
															onSelect={() => {
																form.setValue('currency', currency.currency);
															}}
														>
															{`${currency.currencySymbol} ${currency.currency} (${currency.currencyName})`}
															<Check
																className={cn(
																	'ml-auto',
																	currency.currency === field.value
																		? 'opacity-100'
																		: 'opacity-0'
																)}
															/>
														</CommandItem>
													))}
												</CommandGroup>
											</CommandList>
										</Command>
									</PopoverContent>
								</Popover>
								<FormDescription>Your Business Currency</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="timeFormat"
						render={({ field }) => (
							<FormItem className="col-span-12 md:col-span-4 lg:col-span-3">
								<FormLabel>Time Format</FormLabel>
								<Select
									onValueChange={field.onChange}
									defaultValue={field.value}
									className="w-full"
								>
									<FormControl>
										<SelectTrigger className="w-full">
											<SelectValue placeholder="Select a Time format" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value="12h">12 Hours</SelectItem>
										<SelectItem value="24h">24 Hours</SelectItem>
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={form.control}
						name="dateFormat"
						render={({ field }) => (
							<FormItem className="col-span-12 md:col-span-4 lg:col-span-3">
								<FormLabel>Date Format</FormLabel>
								<Select
									onValueChange={field.onChange}
									defaultValue={field.value}
									className="w-full"
								>
									<FormControl>
										<SelectTrigger className="w-full">
											<SelectValue placeholder="Select a Date format" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
										<SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
										<SelectItem value="YYYY-MM-DD">YYYY/MM/DD</SelectItem>
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={form.control}
						name="address"
						render={({ field }) => (
							<FormItem className="col-span-12">
								<FormLabel>Address</FormLabel>
								<FormControl>
									<Textarea
										placeholder="Address"
										{...field}
										className="resize-none"
									/>
								</FormControl>
								<FormDescription>Your Business Address</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>
				</section>
				<LoadingSubmitButton
					isLoading={isLoadingCompany}
					buttonLoadingText={'Registering...'}
					buttonText={'Register'}
				/>
			</form>
		</Form>
	);
}
