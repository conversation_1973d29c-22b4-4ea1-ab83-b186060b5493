const { z } = require('zod');

const personalInfoSchema = z.object({
	name: z
		.string()
		.nonempty('Full Name as per NRIC is required')
		.min(3, 'Full Name must be at least 3 characters long including spaces')
		.max(50, 'Full Name must be at most 50 characters long')
		.regex(/^[a-zA-Z\s]+$/, 'Full Name must contain only letters and spaces'),

	gender: z.enum(['male', 'female', 'other'], {
		errorMap: () => ({
			message: 'Gender must be either male, female, or other',
		}),
	}),

	dob: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date of Birth is required'),

	age: z
		.number()
		.min(18, 'Employee must be at least 18 years old')
		.max(120, 'Age must be below 120 years')
		.optional(),

	nationality: z
		.string()
		.nonempty('Nationality is required')
		.regex(/^[a-f\d]{24}$/i, 'Invalid Nationality ID format'),

	religion: z
		.string()
		.nonempty('Religion is required')
		.min(2, 'Religion must be at least 2 characters long')
		.max(50, 'Religion must be at most 50 characters long')
		.regex(/^[a-zA-Z\s]+$/, 'Religion must contain only letters and spaces'),

	race: z
		.string()
		.nonempty('Race is required')
		.min(2, 'Race must be at least 2 characters long')
		.max(50, 'Race must be at most 50 characters long')
		.regex(/^[a-zA-Z\s-]+$/, 'Race must contain only letters and spaces'),
});

const identificationContactDetailsSchema = z.object({
	icFinPrefix: z.string().nonempty('IC/FIN Prefix is required'),

	icFinNumber: z
		.string()
		.nonempty('IC/FIN Number is required')
		.min(8, 'IC/FIN Number must be exactly 8 characters long excluding prefix')
		.max(8, 'IC/FIN Number must be exactly 8 characters long excluding prefix')
		.regex(
			/^[0-9]{7}[A-Z]$/,
			'IC/FIN Number must be alphanumeric and 8 characters long excluding prefix'
		),
	residentialStatus: z.enum(
		[
			'Singapore Citizen',
			'Singapore PR',
			'Employment Pass',
			'SPass',
			'Work Permit',
			'LOC',
		],
		{
			errorMap: () => ({
				message:
					'Residential Status must be one of: Singapore Citizen, Singapore PR, Employment Pass, SPass, Work Permit, or LOC',
			}),
		}
	),
	countryDialCode: z
		.string()
		.max(4, 'Dial code can be at most 4 characters long')
		.nonempty('Country dial code is required'),

	mobile: z
		.string()
		.nonempty('Mobile number is required')
		.min(8, 'Mobile number must be at least 8 digits long')
		.max(15, 'Mobile number must be at most 15 digits long')
		.regex(/^\d+$/, 'Mobile number must contain only numbers'),

	issueDate: z.string().optional(),

	email: z.string().nonempty('Email is required').email('Invalid email format'),
});

const addressSchema = z.object({
	country: z
		.string()
		.nonempty('Country is required in address')
		.regex(/^[a-f\d]{24}$/i, 'Invalid Country ID format'),

	postalCode: z
		.string()
		.nonempty('Postal Code is required')
		.regex(/^\d{6}$/, 'Postal Code must be exactly 6 digits'),

	streetName: z
		.string()
		.nonempty('Street Name is required')
		.min(2, 'Street Name must be at least 2 characters long')
		.max(100, 'Street Name must be at most 100 characters long'),

	houseNo: z
		.string()
		.nonempty('House Number is required')
		.min(1, 'House Number must be at least 1 character long')
		.max(10, 'House Number must be at most 10 characters long'),

	levelNo: z
		.string()
		.min(1, 'Level Number must be at least 1 character long')
		.max(5, 'Level Number must be at most 5 characters long')
		.optional(),

	unitNo: z
		.string()
		.min(1, 'Unit Number must be at least 1 character long')
		.max(5, 'Unit Number must be at most 5 characters long')
		.optional(),

	address: z
		.string()
		.nonempty('Address is required')
		.min(5, 'Address must be at least 5 characters long')
		.max(200, 'Address must be at most 200 characters long')
		.regex(
			/^[a-zA-Z0-9\s,.\-#]+$/,
			'Address must contain only letters, numbers, spaces, commas, periods, hashtags and hyphens'
		),
});

const maritalSchema = z.object({
	maritalStatus: z.enum(['single', 'married', 'other', 'prefer-not-to-say'], {
		errorMap: () => ({
			message: 'Please select a valid marital status',
		}),
	}),
	spouseName: z.string().nullable().optional(),
	spouseEmploymentStatus: z
		.enum(['employed', 'unemployed', 'prefer-not-to-say', ''], {
			errorMap: () => ({
				message: 'Please select a valid employment status',
			}),
		})
		.nullable()
		.optional(),
});

const childrenSchema = z.object({
	children: z
		.array(
			z.object({
				name: z.string().nonempty('Name is required'),
				dob: z
					.string()
					.regex(/^\d{4}-\d{2}-\d{2}$/, 'Date of Birth is required'),
				age: z
					.number()
					.min(0, 'Age must be a positive number')
					.max(100, 'Age must be below 100')
					.optional(),
				nationality: z
					.string()
					.nonempty('Nationality is required')
					.min(4, 'Nationality must be at least 4 characters long')
					.max(50, 'Nationality must be at most 50 characters long')
					.regex(
						/^[a-zA-Z\s]+$/,
						'Nationality must contain only letters and spaces'
					),
			})
		)
		.optional(),
});

const basePersonalDetailsSchema = z.object({
	...personalInfoSchema.shape,
	...identificationContactDetailsSchema.shape,
	...addressSchema.shape,
	employeeOrgId: z
		.string()
		.nonempty('Employee ID is required')
		.min(4, 'Employee ID must be at least 4 characters long'),

	dateOfJoining: z
		.string()
		.regex(/^\d{4}-\d{2}-\d{2}$/, 'Date of joining is required'),

	expiryDate: z.string().optional(),

	expiryDateReminder: z.string().optional(),
});

const personalDetailsSchema = z
	.object({
		...basePersonalDetailsSchema.shape,
		...maritalSchema.shape,
		...childrenSchema.shape,
	})
	.superRefine((data, ctx) => {
		if (data.maritalStatus === 'married') {
			if (!data.spouseName) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: "Spouse Name is required when marital status is 'married'",
					path: ['spouseName'],
				});
			}
			if (!data.spouseEmploymentStatus) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message:
						"Spouse Employment Status is required when marital status is 'married'",
					path: ['spouseEmploymentStatus'],
				});
			}
			if (
				data.issueDate &&
				data.expiryDate &&
				new Date(data.issueDate) >= new Date(data.expiryDate)
			) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: 'Expiry Date must be after Issue Date',
					path: ['expiryDate'],
				});
			}
			if (
				new Date(data.expiryDate).setHours(0, 0, 0, 0) <=
				new Date().setHours(0, 0, 0, 0)
			) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: 'Expired',
					path: ['expiryDate'],
				});
			}

			if (data.nationality !== 'Singapore' && !data.issueDate) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: 'Issue Date is Required',
					path: ['issueDate'],
				});
			}
			if (data.expiryDateReminder) {
				const expiryDate = new Date(data.expiryDate);
				const reminderDate = new Date(data.expiryDateReminder);
				const differenceInDays =
					(expiryDate - reminderDate) / (1000 * 60 * 60 * 24);

				if (differenceInDays > 180) {
					ctx.addIssue({
						code: z.ZodIssueCode.custom,
						message:
							'Expiry date reminder cannot be more than 180 days before expiry date',
						path: ['expiryDateReminder'],
					});
				}
			}
		}
	});

const educationAndSkillsSchema = z
	.object({
		educationalDetails: z
			.array(
				z
					.object({
						instituteName: z
							.string()
							.max(100, 'Institute Name must be at most 100 characters long')
							.optional(),

						qualification: z
							.enum(
								['UNDER_GRADUATE', 'POST_GRADUATE', 'NO_FORMAL_EDUCATION'],
								{
									errorMap: () => ({
										message: 'Select qualification',
									}),
								}
							)
							.optional(),

						grade: z
							.string()
							.max(50, 'Major must be at most 50 characters long')
							.optional(),

						startDate: z.string(),
						// .regex(/^\d{4}-\d{2}-\d{2}$/, 'Start date is required'),

						endDate: z.string(),
						// .regex(/^\d{4}-\d{2}-\d{2}$/, 'End Date is required'),

						// document: imageAndPdfMediaSchema.optional(),
					})
					.superRefine((data, ctx) => {
						if (
							data.startDate &&
							data.endDate &&
							data.startDate >= data.endDate
						) {
							ctx.addIssue({
								code: z.ZodIssueCode.custom,
								message: 'End Date must be after Start Date',
								path: ['endDate'], // Error highlights `endDate` field
							});
						}
						if (
							data.qualification !== 'NO_FORMAL_EDUCATION' ||
							!data.qualification
						) {
							if (!data.instituteName) {
								ctx.addIssue({
									code: z.ZodIssueCode.custom,
									message: 'Institute name is required',
									path: ['instituteName'],
								});
							}
							if (!data.grade) {
								ctx.addIssue({
									code: z.ZodIssueCode.custom,
									message: 'Grade is required',
									path: ['grade'],
								});
							}
							if (!data.startDate) {
								ctx.addIssue({
									code: z.ZodIssueCode.custom,
									message: 'Start date is required',
									path: ['startDate'],
								});
							}
							if (!data.endDate) {
								ctx.addIssue({
									code: z.ZodIssueCode.custom,
									message: 'End date is required',
									path: ['endDate'],
								});
							}
						}
					})
			)
			.max(5, 'Maximum 5 Educational Details are accepted.'),

		hardSkills: z.array(z.string().min(2).max(50)).default([]),

		softSkills: z.array(z.string().min(2).max(50)).default([]),
	})
	.strict();

const contactDetailsFormSchema = z
	.object({
		contacts: z
			.array(
				z
					.object({
						type: z.enum(['reference', 'emergency'], {
							errorMap: () => ({
								message:
									"Contact type must be either 'reference' or 'emergency'",
							}),
						}),
						name: z
							.string()
							.min(2, 'Name must be at least 2 characters long')
							.max(50, 'Name must be at most 50 characters long')
							.optional(),
						relationship: z
							.string()
							.nonempty('Relationship is required')
							.min(2, 'Relationship must be at least 2 characters long')
							.max(50, 'Relationship must be at most 50 characters long'),
						countryDialCode: z
							.string()
							.max(4, 'Dial code can be atmost 4 characters long')
							.nonempty('Country dial code is required'),
						phone: z
							.string()
							.nonempty('Phone number is required')
							.min(8, 'Phone number must be at least 8 digits long')
							.max(15, 'Phone number must be at most 15 digits long')
							.regex(/^\d+$/, 'Phone number must contain only numbers'),
						email: z.string().email('Invalid email format').optional(),
					})
					.superRefine((data, ctx) => {
						if (data.type === 'emergency' && !data.name) {
							ctx.addIssue({
								code: z.ZodIssueCode.custom,
								message: 'Name is required for emergency contacts',
								path: ['name'],
							});
						}
						if (data.type === 'reference' && !data.email) {
							ctx.addIssue({
								code: z.ZodIssueCode.custom,
								message: 'Email is required for reference contacts',
								path: ['email'],
							});
						}
					})
			)
			.min(1, 'At least one contacts is required'),
		// .refine(
		// 	(contacts) =>
		// 		contacts.some((contact) => contact.type === 'emergency') &&
		// 		contacts.some((contact) => contact.type === 'reference'),
		// 	{
		// 		message:
		// 			'You must provide at least one emergency contact and one reference contact',
		// 	}
		// ),
	})
	.strict();

const equipmentSchema = z
	.object({
		equipmentName: z
			.string()
			.trim()
			.min(2, 'Equipment name must be at least 2 characters long')
			.max(50, 'Equipment name must be at most 50 characters long'),
		brand: z
			.string()
			.trim()
			.min(2, 'Brand must be at least 2 characters long')
			.max(50, 'Brand must be at most 50 characters long'),
		model: z
			.string()
			.trim()
			.min(2, 'Model must be at least 2 characters long')
			.max(50, 'Model must be at most 50 characters long'),
		serialNumber: z.string().nonempty('Serial number is required'),
		issueDate: z
			.string()
			.regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid issue date format'),
		returnDate: z.string().optional(),
		issueReason: z.enum(['new-hire', 'replacement', 'repair', 'other']),
		otherIssueReason: z.string().optional(),
		returnReason: z
			.enum(['damaged', 'end-of-contract', 'upgrade', 'other'])
			.optional(),
		otherReturnReason: z.string().optional(),
		assetTag: z.string().nonempty('Asset tag is required'),
	})
	.superRefine((data, ctx) => {
		if (data.issueReason === 'other' && !data.otherIssueReason) {
			ctx.addIssue({
				code: z.ZodIssueCode.custom,
				message: 'Please specify the other issue reason',
				path: ['otherIssueReason'],
			});
		}
		if (data.returnReason === 'other' && !data.otherReturnReason) {
			ctx.addIssue({
				code: z.ZodIssueCode.custom,
				message: 'Please specify the other return reason',
				path: ['otherReturnReason'],
			});
		}
	});

const employmentDetailsSchema = z
	.object({
		source: z.enum(['staff-recommendation', 'job-advertisement'], {
			errorMap: () => ({
				message:
					"Source must be either 'staff-recommendation' or 'job-advertisement'",
			}),
		}),

		businessUnit: z
			.string()
			.nonempty('Business unit is required')
			.min(2, 'Business unit must be at least 2 characters long')
			.max(100, 'Business unit must be at most 100 characters long'),

		department: z
			.string()
			.nonempty('Department is required')
			.min(2, 'Department must be at least 2 characters long')
			.max(100, 'Department must be at most 100 characters long'),

		designation: z
			.string()
			.nonempty('Designation is required')
			.min(2, 'Designation must be at least 2 characters long')
			.max(100, 'Designation must be at most 100 characters long'),

		reportingTo: z
			.string()
			.nonempty('Reporting manager ID is required')
			.min(24, 'Invalid MongoDB Object ID')
			.max(24, 'Invalid MongoDB Object ID')
			.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID'),

		employeeRole: z
			.string()
			.nonempty('Employee role is required')
			.min(1, 'Role must be at least 1 characters long')
			.max(2, 'Role must be at most 2 characters long'),

		probationPeriod: z.string().refine(
			(val) => {
				const num = parseInt(val, 10);
				return !isNaN(num) && num >= 0 && num <= 12;
			},
			{
				message: 'Probation period must be an integer between 0 and 12',
			}
		),
		employmentType: z.enum(['part-time', 'full-time'], {
			errorMap: () => ({
				message: "Employment type must be either 'part-time' or 'full-time'",
			}),
		}),

		workSchedule: z
			.enum(['shifts', 'generic'], {
				errorMap: () => ({
					message: "Work schedule must be either 'shifts' or 'generic'",
				}),
			})
			.optional(),

		// workingDays: z
		// 	.enum(['5_DAYS', '5.5_DAYS', '6_DAYS', 'ALTERNATE_SATURDAYS'], {
		// 		errorMap: () => ({
		// 			message: 'Working Days invalid',
		// 		}),
		// 	})
		// 	.optional(),

		// workingHours: z
		// 	.enum(['4', '6', '8', '10', '12'], {
		// 		errorMap: () => ({
		// 			message: 'Please provide valid working hours',
		// 		}),
		// 	})
		// 	.optional(),

		// firstOffDay: z
		// 	.enum(
		// 		[
		// 			'SUNDAY',
		// 			'MONDAY',
		// 			'TUESDAY',
		// 			'WEDNESDAY',
		// 			'THURSDAY',
		// 			'FRIDAY',
		// 			'SATURDAY',
		// 		],
		// 		{
		// 			errorMap: () => ({
		// 				message: 'Please select valid day.',
		// 			}),
		// 		}
		// 	)
		// 	.optional(),
		// secondOffDay: z
		// 	.enum(
		// 		[
		// 			'SUNDAY',
		// 			'MONDAY',
		// 			'TUESDAY',
		// 			'WEDNESDAY',
		// 			'THURSDAY',
		// 			'FRIDAY',
		// 			'SATURDAY',
		// 		],
		// 		{
		// 			errorMap: () => ({
		// 				message: 'Please select valid day.',
		// 			}),
		// 		}
		// 	)
		// 	.optional(),
		// halfDay: z
		// 	.enum(['FIRST_HALF', 'SECOND_HALF'], {
		// 		errorMap: () => ({
		// 			message: 'Please select either first half or second half',
		// 		}),
		// 	})
		// 	.optional(),

		shiftId: z
			.string()
			.nonempty('Shift ID is required')
			.regex(/^[a-f\d]{24}$/, 'Invalid shift ID'),

		isBlocked: z.boolean().default(false).optional(),

		overTimeEligible: z.boolean().default(false),
		equipment: z
			.array(equipmentSchema)
			.max(3, 'At most three equipment items allowed')
			.optional(),
	})
	.strict()
	.superRefine((data, ctx) => {
		// 1️⃣ Full-time employees with shifts must have working days
		// if (data.employmentType === 'full-time' && data.workSchedule === 'shifts') {
		// 	if (!data.workingDays || data.workingDays.length === 0) {
		// 		ctx.addIssue({
		// 			code: z.ZodIssueCode.custom,
		// 			message:
		// 				'Working days are required for full-time employees with shift schedules',
		// 			path: ['workingDays'],
		// 		});
		// 	}
		// }

		// // 2️⃣ First and Second Off Day should not be the same
		// if (
		// 	data.firstOffDay &&
		// 	data.secondOffDay &&
		// 	data.firstOffDay === data.secondOffDay
		// ) {
		// 	ctx.addIssue({
		// 		code: z.ZodIssueCode.custom,
		// 		message: 'First and Second off day cannot be the same',
		// 		path: ['secondOffDay'],
		// 	});
		// }

		// // 3️⃣ Full-time employees must have working hours specified
		// if (data.employmentType === 'full-time' && !data.workingHours) {
		// 	ctx.addIssue({
		// 		code: z.ZodIssueCode.custom,
		// 		message: 'Working hours must be provided for full-time employees',
		// 		path: ['workingHours'],
		// 	});
		// }

		// // 4️⃣ Generic work schedules with 5.5 working days require a Half Day selection
		// if (
		// 	data.workSchedule === 'generic' &&
		// 	data.workingDays === '5.5_DAYS' &&
		// 	!data.halfDay
		// ) {
		// 	ctx.addIssue({
		// 		code: z.ZodIssueCode.custom,
		// 		message: 'Half day selection is required for generic work schedules',
		// 		path: ['halfDay'],
		// 	});
		// }

		// // 5️⃣ 5.5 working days require a Second Off Day selection
		// if (data.workingDays === '5.5_DAYS' && !data.secondOffDay) {
		// 	ctx.addIssue({
		// 		code: z.ZodIssueCode.custom,
		// 		message: 'Second off day selection is required for 5.5 working days',
		// 		path: ['halfDay'],
		// 	});
		// }

		// 6️⃣ If equipment is provided, at least one item must have a serial number
		if (data.equipment && data.equipment.length > 0) {
			const hasSerialNumber = data.equipment.some(
				(item) => item.serialNumber && item.serialNumber.trim() !== ''
			);
			if (!hasSerialNumber) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: 'At least one equipment item must have a serial number',
					path: ['equipment'],
				});
			}
		}
	});

const earningsDetailsSchema = z
	.object({
		basicPay: z.object({
			currency: z.string(),
			amount: z.number().positive('Basic pay cannot be 0 or negative'),
		}),

		paymentMode: z.enum(['cash/cheque', 'bank']),

		frequency: z.enum(['daily', 'weekly', 'monthly']),
		payBasis: z.enum(['hourly', 'daily', 'weekly', 'monthly']),

		dailyRate: z.number().positive('Daily rate cannot be 0 or negative'),
		hourlyRate: z.number().positive('Hourly rate cannot be 0 or negative'),
		weeklyRate: z.number().positive('Weekly rate cannot be 0 or negative'),
		yearlyRate: z.number().positive('Yearly rate cannot be 0 or negative'),
		overtimeRate: z.number('Overtime rate cannot be 0 or negative'),

		isSalaryAdvanceEligible: z.boolean().default(false),
		salaryAdvance: z
			.number()
			.nonnegative('Salary Advance cannot be negative')
			.optional(),

		// Bank details — optional by default, but required in superRefine
		bankName: z.string().optional(),
		accountNumber: z.string().optional(),
		accountHolderName: z.string().optional(),
		bankCode: z.string().optional(),
		swiftBIC: z.string().optional(),
		branchCode: z.string().optional(),
	})
	.superRefine((data, ctx) => {
		// Salary advance validation
		if (data.isSalaryAdvanceEligible) {
			if (!data.salaryAdvance) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: 'Salary advance amount is required',
					path: ['salaryAdvance'],
				});
			}
			if (data.basicPay.amount === 0 && data.salaryAdvance > 0) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: 'Please enter basic pay amount first',
					path: ['salaryAdvance'],
				});
			}
			if (data.salaryAdvance > data.basicPay.amount) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: 'Salary advance amount cannot be greater than basic pay',
					path: ['salaryAdvance'],
				});
			}
		}

		// Bank details conditional validation
		if (data.paymentMode === 'bank') {
			const bankFields = [
				'bankName',
				'accountNumber',
				'accountHolderName',
				'bankCode',
				'swiftBIC',
				'branchCode',
			];

			bankFields.forEach((field) => {
				if (!data[field]) {
					ctx.addIssue({
						code: z.ZodIssueCode.custom,
						message: `${field} is required when payment mode is bank`,
						path: [field],
					});
				}
			});
		}
	});

const benefitsSchema = z.object({
	holiday: z.object({
		eligibleForOffInLieu: z.boolean().default(false),
		assigned: z.array(z.string()).optional(),
	}),
	leave: z.object({
		eligibleForOffInLieu: z.boolean().default(false),
		code: z.string().optional(),
		type: z.string().optional(),
		eligibility: z.string().optional(),
		leaveIncrement: z
			.number()
			.min(0, 'Leave increment must be positive')
			.optional(),
		hourlyTimeOff: z.boolean().default(false),
		prorate: z.boolean().default(false),
	}),
	health: z.object({
		eligibleForOffInLieu: z.boolean().default(false),
		assigned: z.array(z.string()).optional(),
	}),
});

const holidaySchema = z.object({
	groupName: z.string().min(1, 'Group name is required').toLowerCase(),
	holidays: z.array(
		z
			.object({
				title: z.string().min(1, 'Holiday title is required'),
				startDate: z.coerce.date(),
				endDate: z.coerce.date(),
				numberOfDays: z
					.number()
					.optional()
					.default(0)
					.refine(() => true, {
						// Dummy refine to compute dynamically
						message: 'Number of days is computed automatically',
					}),
				icon: z.string().min(1, 'Holiday icon is required'),
			})
			.superRefine((data, ctx) => {
				if (data.startDate && data.endDate) {
					const days =
						dayjs(data.endDate).diff(dayjs(data.startDate), 'day') + 1;
					if (days < 1) {
						ctx.addIssue({
							code: z.ZodIssueCode.custom,
							message: 'End date must be after start date',
							path: ['endDate'],
						});
					}
					data.numberOfDays = days;
				}
			})
	),
});

const offboardingFormSchema = z.object({
	dateOfLeave: z
		.string()
		.regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')
		.transform((val) => new Date(val)),
	reason: z
		.string()
		.nonempty('Reason for leave is needed')
		.max(200, 'Reason should not be more than 200 characters long'),
	exitInterviewConducted: z.boolean().default(false),
	interviewConductedBy: z
		.string()
		.nonempty('Employee ID is required')
		.regex(/^[a-f\d]{24}$/i, 'Invalid Employee ID format'),
});

export {
	personalInfoSchema,
	identificationContactDetailsSchema,
	addressSchema,
	maritalSchema,
	childrenSchema,
	personalDetailsSchema,
	basePersonalDetailsSchema,
	educationAndSkillsSchema,
	contactDetailsFormSchema,
	employmentDetailsSchema,
	earningsDetailsSchema,
	benefitsSchema,
	offboardingFormSchema,
};
