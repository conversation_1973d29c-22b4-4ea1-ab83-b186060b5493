import { useState, useRef, useMemo, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { onboardingStepOneSchema } from '@/lib/schemas/onboardingSchema';
import { useVirtualizer } from '@tanstack/react-virtual';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from './ui/form';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Button } from './ui/button';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { cn, countryData, countryDataFormats } from '@/lib/utils';
import { Check, ChevronsUpDown, Loader2 } from 'lucide-react';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from './ui/command';
import { currencyData } from '@/data/currencyData';
import {
	fetchFileFromBlobURL,
	onboardingStepOne,
	updateForm,
	setCurrency,
} from '@/lib/features/client-admin/clientAdminSlice';
import { fetchCountries } from '@/lib/features/location/locationSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { LoadingSubmitButton } from './loading-component';

export function BusinessDetails() {
	const [country, setCountry] = useState(null);
	const [registrationDetails, setRegistrationDetails] = useState(null);
	const dispatch = useAppDispatch();
	const { companyData, logo } = useAppSelector((store) => store.clientAdmin);
	const { isLoading, countries, isLoadingCountries } = useAppSelector(
		(store) => store.location
	);
	const form = useForm({
		mode: 'onChange',

		resolver: zodResolver(onboardingStepOneSchema),
		defaultValues: {
			businessName: '',
			businessCountry: '',
			businessCountryDialCode: '',
			registration: '',
			address: '',
		},
	});

	useEffect(() => {
		const country = countryDataFormats.find(
			(c) => c.country === form.watch('businessCountry')
		);
		setRegistrationDetails(country?.registration);
	}, [form.watch('businessCountry')]);

	useEffect(() => {
		const subscription = form.watch((value) => {
			dispatch(updateForm(value));
		});
		return () => subscription.unsubscribe();
	}, [form.watch, dispatch, form]);

	useEffect(() => {
		dispatch(fetchCountries());
	}, [dispatch]);

	const onSubmit = async (data) => {
		const formData = new FormData();
		Object.entries(data).forEach(([key, value]) => {
			if (key === 'businessCountryDialCode') {
				return;
			}
			if (key === 'businessCountry' && country) {
				formData.append(key, country._id);
				return;
			}
			formData.append(key, value);
		});
		const logoFile = await fetchFileFromBlobURL(logo?.url, logo?.name);
		formData.append('logo', logoFile);
		dispatch(onboardingStepOne(formData));
	};

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-4">
				<section className="grid grid-cols-12 gap-2">
					{/* Business Name */}
					<FormField
						control={form.control}
						name="businessName"
						render={({ field }) => (
							<FormItem className="col-span-12">
								<FormLabel>Business Name</FormLabel>
								<FormControl>
									<Input placeholder="Business Name" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Country Selection with Virtualized Dropdown */}

					<FormField
						control={form.control}
						name="businessCountry"
						render={({ field }) => (
							<FormItem className=" col-span-8">
								<FormLabel>Country</FormLabel>
								<Popover>
									<PopoverTrigger asChild>
										<FormControl>
											<Button
												variant="outline"
												role="combobox"
												className={cn(
													'w-full justify-between',
													!field.value && 'text-muted-foreground'
												)}
											>
												{field.value
													? countries.find(
															(country) => country.name === field.value
														)?.name
													: 'Select country'}
												{isLoadingCountries && (
													<Loader2 className="h-4 w-4 animate-spin" />
												)}
												{!isLoadingCountries && (
													<ChevronsUpDown className="opacity-50" />
												)}
											</Button>
										</FormControl>
									</PopoverTrigger>
									<PopoverContent className="w-full p-0">
										<Command>
											<CommandInput
												placeholder="Search Country..."
												className="h-9"
											/>
											<CommandList>
												<CommandEmpty>No Country found.</CommandEmpty>
												<CommandGroup>
													{countries.map((country) => (
														<CommandItem
															value={country.name}
															key={country._id}
															onSelect={() => {
																form.setValue('businessCountry', country.name);
																form.setValue(
																	'businessCountryDialCode',
																	`+${country.phoneCode}`
																);
																setCountry(country);
																dispatch(setCurrency(country.currency));
																dispatch(
																	updateForm({ selectedCountryId: country._id })
																);
															}}
														>
															{country.name}
															<Check
																className={cn(
																	'ml-auto',
																	country._id === field.value
																		? 'opacity-100'
																		: 'opacity-0'
																)}
															/>
														</CommandItem>
													))}
												</CommandGroup>
											</CommandList>
										</Command>
									</PopoverContent>
								</Popover>
								<FormDescription>Your country of business.</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Country Code */}
					<FormField
						control={form.control}
						name="businessCountryDialCode"
						render={({ field }) => (
							<FormItem className="col-span-4">
								<FormLabel>Country Dial Code</FormLabel>
								<FormControl>
									<Input placeholder="Country Dial Code" {...field} disabled />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Registration */}
					<FormField
						control={form.control}
						name="registration"
						render={({ field }) => (
							<FormItem className="col-span-12">
								<FormLabel>{registrationDetails?.label} Registration</FormLabel>
								<FormControl>
									<Input placeholder="Registration" {...field} />
								</FormControl>
								<FormDescription>
									{registrationDetails?.description ||
										'Your Business Registration Number'}
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Address */}
					<FormField
						control={form.control}
						name="address"
						render={({ field }) => (
							<FormItem className="col-span-12">
								<FormLabel>Address</FormLabel>
								<FormControl>
									<Textarea
										placeholder="Address"
										{...field}
										className="resize-none"
									/>
								</FormControl>
								<FormDescription>Your Business Address</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>
				</section>
				<LoadingSubmitButton
					isLoading={isLoading}
					buttonLoadingText={'Registering...'}
					buttonText={'Register'}
				/>
			</form>
		</Form>
	);
}
