'use client';

import { useEffect, useState } from 'react';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
	Clock,
	Users,
	Briefcase,
	TrendingUp,
	Search,
	Filter,
} from 'lucide-react';
import ProjectLogsTable from './table';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { getAttendanceProjectLogs } from '@/lib/features/attendance/attendanceSlice';
import { SimpleLoader } from '@/components/loading-component';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
dayjs.extend(duration);

export default function ProjectLogsPage() {
	const dispatch = useAppDispatch();
	const { projectLogs, isLoading } = useAppSelector(
		(store) => store.attendance
	);
	const [searchTerm, setSearchTerm] = useState('');
	const [departmentFilter, setDepartmentFilter] = useState('all');
	let totalMinutes = 0;
	const allProjects = new Set();
	projectLogs?.map((employee) => {
		totalMinutes += employee.totalWorkingMinutes;
		allProjects.add(...employee.projects);
	});
	const totalHours = dayjs.duration(totalMinutes, 'minutes').format('HH:mm');

	useEffect(() => {
		dispatch(getAttendanceProjectLogs());
	}, [dispatch]);

	if (isLoading) return <SimpleLoader />;

	// Filter data for summary stats
	// const filteredData = projectLogs.filter((employee) => {
	// 	if (
	// 		searchTerm &&
	// 		!employee.name.toLowerCase().includes(searchTerm.toLowerCase())
	// 	)
	// 		return false;
	// 	if (departmentFilter !== 'all' && employee.department !== departmentFilter)
	// 		return false;
	// 	return true;
	// });

	return (
		<section className="w-full h-full p-4">
			<header className="flex flex-col gap-2">
				<h1 className="text-xl md:text-2xl font-semibold">Project Logs</h1>
				<p className="font-medium text-gray-500">
					Track employee project assignments and performance metrics.
				</p>
				<Separator />
			</header>

			<main className="grid flex-1 items-start gap-4 md:gap-8 w-full mt-4">
				{/* Filters */}
				{/* <div className="flex gap-4 items-center flex-wrap">
					<div className="relative flex-1 min-w-[200px] max-w-sm">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
						<Input
							placeholder="Search employees..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10"
						/>
					</div>

					<Select value={departmentFilter} onValueChange={setDepartmentFilter}>
						<SelectTrigger className="w-48">
							<Filter className="h-4 w-4 mr-2" />
							<SelectValue placeholder="Filter by department" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">All Departments</SelectItem>
							<SelectItem value="Engineering">Engineering</SelectItem>
							<SelectItem value="Design">Design</SelectItem>
							<SelectItem value="Marketing">Marketing</SelectItem>
						</SelectContent>
					</Select>

					<Select value={statusFilter} onValueChange={setStatusFilter}>
						<SelectTrigger className="w-48">
							<SelectValue placeholder="Filter by status" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">All Status</SelectItem>
							<SelectItem value="active">Active</SelectItem>
							<SelectItem value="inactive">Inactive</SelectItem>
						</SelectContent>
					</Select>

					<Button variant="outline" size="sm">
						<Download className="h-4 w-4 mr-2" />
						Export Data
					</Button>
				</div> */}

				{/* Summary Stats */}
				<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
					<Card>
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Total Employees
									</p>
									<p className="text-3xl font-bold">{projectLogs?.length}</p>
								</div>
								<Users className="h-8 w-8 text-muted-foreground" />
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Total Hours
									</p>
									<p className="text-3xl font-bold">{totalHours}h</p>
								</div>
								<Clock className="h-8 w-8 text-muted-foreground" />
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Active Projects
									</p>
									<p className="text-3xl font-bold">{allProjects.size}</p>
								</div>
								<Briefcase className="h-8 w-8 text-muted-foreground" />
							</div>
						</CardContent>
					</Card>

					{/* <Card>
try:
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Avg Performance
									</p>
									<p className="text-3xl font-bold">
										{filteredData.length > 0
											? Math.round(
													filteredData.reduce(
														(sum, emp) =>
															sum +
															(emp.performance.efficiency +
																emp.performance.quality +
																emp.performance.collaboration) /
																3,
														0
													) / filteredData.length
												)
											: 0}
										%
									</p>
								</div>
								<TrendingUp className="h-8 w-8 text-muted-foreground" />
							</div>
						</CardContent>
					</Card> */}
				</div>

				{/* Data Table */}
				<Card>
					<CardHeader>
						<CardTitle>Employee Project Data</CardTitle>
						<CardDescription>
							Comprehensive view of employee project assignments and performance
						</CardDescription>
					</CardHeader>
					<CardContent>
						<ProjectLogsTable />
					</CardContent>
				</Card>
			</main>
		</section>
	);
}
