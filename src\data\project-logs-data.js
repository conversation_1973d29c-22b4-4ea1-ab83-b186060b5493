// Response of calender API
[
  {
    month: "2025-07",
    monthName: "July",
    days: [
      {
        createdAt: "2025-07-24T00:00:00.000Z",
      },
      {
        attendanceTime: [
          {
            type: "clockIn",
            time: "2025-07-25T05:09:57.074Z",
            _id: "688311a59d01f866575c27d1",
          },
          {
            type: "start-break",
            time: "2025-07-25T05:14:59.479Z",
            _id: "688312d39d01f866575c2883",
          },
          {
            type: "end-break",
            time: "2025-07-25T12:27:41.467Z",
            _id: "6883783d1e257ad98e40667d",
          },
          {
            type: "clockOut",
            time: "2025-07-25T12:44:40.443Z",
            _id: "68837c381e257ad98e40681f",
          },
        ],
        projectStats: {
          projectId: "68823558e764f413f03e3e12",
          workingHours: 448,
          isWorking: false,
          _id: "688311a59d01f866575c27d2",
        },
        createdAt: "2025-07-25T05:09:57.309Z",
      },
      {
        createdAt: "2025-07-26T00:00:00.000Z",
      },
      {
        createdAt: "2025-07-27T00:00:00.000Z",
      },
      {
        attendanceTime: [
          {
            type: "clockIn",
            time: "2025-07-28T02:28:11.519Z",
            _id: "6886e03b679055fd5a18543f",
          },
          {
            type: "clockOut",
            time: "2025-07-28T18:00:00.000Z",
            _id: "68881d1099ce9ba9db952662",
          },
        ],
        projectStats: {
          projectId: "68823558e764f413f03e3e12",
          workingHours: 0,
          isWorking: true,
          _id: "6886e03b679055fd5a185440",
        },
        createdAt: "2025-07-28T02:28:11.746Z",
      },
      {
        createdAt: "2025-07-29T00:00:00.000Z",
      },
      {
        createdAt: "2025-07-30T00:00:00.000Z",
      },
      {
        createdAt: "2025-07-31T00:00:00.000Z",
      },
    ],
    label: "July 2025",
  },
  {
    month: "2025-08",
    monthName: "August",
    days: [
      {
        createdAt: "2025-08-01T00:00:00.000Z",
      },
    ],
    label: "August 2025",
  },
];

// Response of user project logs API
{
  totalWorkingMinutes: 463,
  projects: [
    {
      projectId: "68823558e764f413f03e3e12",
      projectName: "hire bomm.ai",
      totalWorkingMinutes: 448,
    },
    {
      projectId: "68749f05e86351fe2d611567",
      projectName: "harp hr",
      totalWorkingMinutes: 15,
    },
  ],
  userId: "6881965153938787e88a253e",
  name: "Nanda Kumar",
  email: "<EMAIL>",
  profilePhoto:
    "https://res.cloudinary.com/dv8nbs25p/image/upload/v1753325671/HarpHr/fa31uazik9iewlzpqlxc.jpg",
  department: "HR Test Dept",
  designation: "HR Test Manager",
  mobile: "91452114",
  countryDialCode: "+65",
  joinedAt: "2025-07-24T02:54:32.132Z",
  country: "Angola",
};
