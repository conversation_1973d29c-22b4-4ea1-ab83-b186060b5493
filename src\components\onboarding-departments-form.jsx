'use client';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { motion } from 'framer-motion';
import { Plus, TrashIcon } from 'lucide-react';
import { Button } from './ui/button';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from './ui/form';
import { Input } from './ui/input';
import { Separator } from './ui/separator';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { fadeInUp, staggerContainer } from '@/lib/animation-variants';
import {
	onboardingFinalStep,
	setSteps,
	updateDepartments,
} from '@/lib/features/client-admin/clientAdminSlice';
import { useRouter } from 'next/navigation';

// Create schema for departments
const departmentSchema = z.object({
	departmentName: z
		.string()
		.min(2, 'Department name must be at least 2 characters'),
});

const branchDepartmentsSchema = z.object({
	branchId: z.string(),
	departments: z
		.array(departmentSchema)
		.max(3, 'Maximum 3 departments per branch'),
});

const departmentsSchema = z.object({
	branchDepartments: z.array(branchDepartmentsSchema),
});

export function OnboardingDepartmentsForm() {
	const dispatch = useAppDispatch();
	const router = useRouter();
	const { branches, departments, currencyAndTimeDetails, onboardingSuccess } =
		useAppSelector((store) => store.clientAdmin);
	const [isInitialized, setIsInitialized] = useState(false);
	const watcherActive = useRef(false);

	// Create a deep copy of the departments to avoid mutation issues
	const createDeepCopy = (obj) => {
		return JSON.parse(JSON.stringify(obj || {}));
	};

	// Initialize form with branches and empty departments
	const prepareFormValues = useCallback(() => {
		return {
			branchDepartments: branches.map((branch, index) => {
				// Create deep copies of departments to avoid mutation issues
				const deptsCopy = createDeepCopy(
					departments?.[`branch-${index}`] || []
				);

				return {
					branchId: `branch-${index}`,
					departments: deptsCopy,
				};
			}),
		};
	}, [branches, departments]);

	const form = useForm({
		mode: 'onChange',

		resolver: zodResolver(departmentsSchema),
		defaultValues: prepareFormValues(),
	});

	// Initialize form once when component mounts
	useEffect(() => {
		if (!isInitialized) {
			form.reset(prepareFormValues());
			setIsInitialized(true);
		}
	}, [form, isInitialized, prepareFormValues]);

	// Watch for form changes and update Redux - but only after initialization
	useEffect(() => {
		if (!isInitialized) return;

		// Prevent watching during form reset
		watcherActive.current = true;

		const subscription = form.watch((value) => {
			if (!watcherActive.current) return;

			if (value.branchDepartments) {
				// Debounce updates to prevent rapid firing
				const timer = setTimeout(() => {
					// Transform data for Redux
					const departmentsMap = {};

					value.branchDepartments.forEach((bd) => {
						departmentsMap[bd.branchId] = createDeepCopy(bd.departments || []);
					});

					dispatch(updateDepartments({ departments: departmentsMap }));
				}, 300);

				return () => clearTimeout(timer);
			}
		});

		return () => {
			watcherActive.current = false;
			subscription.unsubscribe();
		};
	}, [isInitialized, dispatch, form]);

	const onSubmit = (data) => {
		dispatch(
			onboardingFinalStep({
				...currencyAndTimeDetails,
				branches,
			})
		);
		// dispatch(setSteps(5)); // Move to next step if needed
	};

	// Helper function to add a department to a branch
	const addDepartment = (branchIndex) => {
		try {
			// Temporarily disable watcher to prevent infinite loops
			watcherActive.current = false;

			// Get current departments and create a deep copy
			const currentDepartments = createDeepCopy(
				form.getValues(`branchDepartments.${branchIndex}.departments`) || []
			);

			if (currentDepartments.length < 3) {
				// Add a new department object
				currentDepartments.push({
					departmentName: '',
					departmentHead: '',
				});

				// Set the new array in the form
				form.setValue(
					`branchDepartments.${branchIndex}.departments`,
					currentDepartments,
					{ shouldDirty: true, shouldTouch: true }
				);

				// Manually update Redux to avoid the watcher
				const formValues = form.getValues();
				const departmentsMap = {};

				formValues.branchDepartments.forEach((bd) => {
					departmentsMap[bd.branchId] = createDeepCopy(bd.departments || []);
				});

				dispatch(updateDepartments({ departments: departmentsMap }));
			}

			// Re-enable watcher after update
			setTimeout(() => {
				watcherActive.current = true;
			}, 100);
		} catch (error) {
			console.error('Error adding department:', error);
			watcherActive.current = true; // Ensure watcher is re-enabled even if there's an error
		}
	};

	// Helper function to remove a department
	const removeDepartment = (branchIndex, deptIndex) => {
		try {
			// Temporarily disable watcher to prevent infinite loops
			watcherActive.current = false;

			// Get current departments and create a deep copy
			const currentDepartments = createDeepCopy(
				form.getValues(`branchDepartments.${branchIndex}.departments`) || []
			);

			// Filter out the department to remove
			const updatedDepartments = currentDepartments.filter(
				(_, i) => i !== deptIndex
			);

			// Set the new array in the form
			form.setValue(
				`branchDepartments.${branchIndex}.departments`,
				updatedDepartments,
				{ shouldDirty: true, shouldTouch: true }
			);

			// Manually update Redux to avoid the watcher
			const formValues = form.getValues();
			const departmentsMap = {};

			formValues.branchDepartments.forEach((bd) => {
				departmentsMap[bd.branchId] = createDeepCopy(bd.departments || []);
			});

			dispatch(updateDepartments({ departments: departmentsMap }));

			// Re-enable watcher after update
			setTimeout(() => {
				watcherActive.current = true;
			}, 100);
		} catch (error) {
			console.error('Error removing department:', error);
			watcherActive.current = true; // Ensure watcher is re-enabled even if there's an error
		}
	};

	useEffect(() => {
		if (onboardingSuccess) {
			router.push('/client-admin');
		}
	}, [onboardingSuccess, router]);

	return (
		<Form {...form}>
			<form
				onSubmit={form.handleSubmit(onSubmit)}
				className="grid gap-6 w-full min-h-80"
			>
				<motion.div
					variants={staggerContainer}
					initial="hidden"
					animate="show"
					className="space-y-6"
				>
					{branches.map((branch, branchIndex) => {
						// Get the form values for this branch's departments
						const formValues = form.getValues();
						const branchDepts =
							formValues?.branchDepartments?.[branchIndex]?.departments || [];

						return (
							<motion.div
								key={`branch-${branch.branchName}-${branchIndex}`}
								variants={fadeInUp}
								className="space-y-4"
							>
								<Card>
									<CardHeader>
										<CardTitle className="text-lg">
											{branch.branchName}
										</CardTitle>
									</CardHeader>
									<CardContent>
										<div className="space-y-4">
											{branchDepts.map((dept, deptIndex) => (
												<motion.div
													key={`dept-${branchIndex}-${deptIndex}`}
													initial={{ opacity: 0, y: 10 }}
													animate={{ opacity: 1, y: 0 }}
													exit={{ opacity: 0, y: -10 }}
													transition={{ duration: 0.2 }}
													className="grid grid-cols-12 gap-2 items-end"
												>
													<FormField
														control={form.control}
														name={`branchDepartments.${branchIndex}.departments.${deptIndex}.departmentName`}
														render={({ field }) => (
															<FormItem className="col-span-11">
																<FormLabel>Department Name</FormLabel>
																<FormControl>
																	<Input
																		placeholder="Enter department name"
																		{...field}
																	/>
																</FormControl>
																<FormMessage />
															</FormItem>
														)}
													/>

													<Button
														variant="outline"
														type="button"
														onClick={() =>
															removeDepartment(branchIndex, deptIndex)
														}
														size="icon"
														className="col-span-1"
													>
														<TrashIcon className="h-4 w-4" />
													</Button>
												</motion.div>
											))}

											{branchDepts.length < 3 && (
												<Button
													type="button"
													variant="outline"
													onClick={() => addDepartment(branchIndex)}
													className="w-full"
												>
													<Plus className="mr-2 h-4 w-4" /> Add Department
												</Button>
											)}
										</div>
									</CardContent>
								</Card>
							</motion.div>
						);
					})}
				</motion.div>

				<Button type="submit" className="mt-auto">
					Submit
				</Button>
			</form>
		</Form>
	);
}
