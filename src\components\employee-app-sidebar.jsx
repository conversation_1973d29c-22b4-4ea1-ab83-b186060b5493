'use client';

import * as React from 'react';
import {
	ArchiveX,
	Banknote,
	BriefcaseBusiness,
	Building,
	Building2,
	CalendarCheck,
	CalendarCheck2,
	CalendarDays,
	ChevronRight,
	ClipboardCheck,
	ClipboardList,
	Command,
	Earth,
	File,
	FileCheck,
	FileClock,
	Fingerprint,
	HandCoins,
	Home,
	Inbox,
	Landmark,
	List,
	Megaphone,
	MessageSquareText,
	Send,
	Settings,
	ShieldCheck,
	Sparkles,
	Trash2,
	TreePalm,
	User,
	Users,
} from 'lucide-react';
import logo from '@/assets/valluva.png';
import { NavUser } from '@/components/nav-user';
import { Label } from '@/components/ui/label';
import {
	Sidebar,
	SidebarContent,
	SidebarFooter,
	SidebarGroup,
	SidebarGroupContent,
	SidebarHeader,
	SidebarInput,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	SidebarMenuSub,
	SidebarMenuSubButton,
	SidebarMenuSubItem,
	useSidebar,
} from '@/components/ui/sidebar';
import { Switch } from '@/components/ui/switch';
import Image from 'next/image';
import Link from 'next/link';
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from './ui/collapsible';
import { ChatsList } from './chats-list';
import { useAppSelector } from '@/lib/hooks';
import { userRoles } from '@/lib/utils';

// This is sample data
const getData = (role, isModuleAdmin, modules) => {
	let navItems = [];
	if (isModuleAdmin) {
		navItems = getNavItems(modules);
	}

	const data = {
		clientNav: [
			{
				title: 'Dashboard',
				url: '/user/',
				icon: Home,
				isActive: true,
				secondaryMenus: [
					{
						icon: User,
						title: 'Profile',
						url: '#',
						items: [
							{
								title: 'Edit Profile',
								url: '/user/profile/edit/',
							},
						],
					},
					// { icon: ClipboardCheck, title: 'Task', url: '#', items: [] },
					// { icon: ClipboardList, title: 'Report', url: '#', items: [] },
					// { icon: Settings, title: 'Settings', url: '#', items: [] },
				],
			},
			{
				title: 'Chat',
				url: '/user/chat/',
				icon: MessageSquareText,
				isActive: false,
				secondaryMenus: [
					{
						icon: Home,
						title: 'Home',
						url: '#',
						items: [
							{
								title: 'Chat Dashboard',
								url: '/user/chat/',
							},
						],
					},
					{
						icon: Inbox,
						title: 'Messages',
						url: '#',
						items: [
							{
								title: 'Inbox',
								url: '/user/chat/inbox/',
							},
							{
								title: 'Sent Messages',
								url: '/user/chat/sent/',
							},
						],
					},
					{
						icon: Settings,
						title: 'Settings',
						url: '#',
						items: [
							{
								title: 'Chat Settings',
								url: '/user/chat/settings/',
							},
							{
								title: 'Message Templates',
								url: '/user/chat/templates/',
							},
						],
					},
				],
			},
			...([userRoles.BUSINESS_ADMIN, userRoles.DEPARTMENT_ADMIN].includes(role)
				? [
						{
							title: 'HR Module',
							url: '/user/hr-module/',
							icon: Users,
							isActive: true,
							secondaryMenus: [
								{
									icon: Home,
									title: 'HR Dashboard',
									url: '#',
									items: [
										{
											title: 'Overview',
											url: '/user/hr-module/',
										},
									],
								},
								{
									icon: List,
									title: 'List',
									url: '#',
									items: [
										{
											title: 'Employees List',
											url: '/user/hr-module/employees-list/',
										},
									],
								},
							],
						},
					]
				: []),
			...navItems,
			{
				title: 'Projects and Tasks',
				url: '/user/projects',
				icon: ClipboardCheck,
				isActive: false,
				secondaryMenus: [
					{
						icon: BriefcaseBusiness,
						title: 'Projects and Tasks',
						url: '/user/tasks/',
						items: [
							{
								title: 'Projects',
								url: '/user/projects/',
							},
						],
					},
				],
			},
		],
	};

	return data;
};

const getNavItems = (modules) => {
	const allModules = {
		hr: {
			title: 'HR Module',
			url: '/user/hr-module/',
			icon: Users,
			isActive: true,
			secondaryMenus: [
				{
					icon: Home,
					title: 'HR Dashboard',
					url: '#',
					items: [
						{
							title: 'Overview',
							url: '/user/hr-module/',
						},
					],
				},
				{
					icon: List,
					title: 'List',
					url: '#',
					items: [
						{
							title: 'Employees List',
							url: '/user/hr-module/employees-list/',
						},
						{
							title: 'Verify & Update',
							url: '/user/hr-module/verify-updates/',
						},
					],
				},
				{
					icon: Users,
					title: 'Employee Transitions',
					url: '#',
					items: [
						// {
						// 	title: 'Link',
						// 	url: '/user/hr-module/employee-link/',
						// },
						{
							title: 'Onboarding',
							url: '/user/hr-module/employee-onboarding/',
						},
						{
							title: 'Offboarding',
							url: '/user/hr-module/employee-offboarding/',
						},
					],
				},
				{
					icon: Settings,
					title: 'Settings',
					url: '#',
					items: [
						{
							title: 'Administration',
							url: '/user/hr-module/administration/',
						},
						{
							title: 'Holiday Management',
							url: '/user/hr-module/holiday-management/',
						},
					],
				},
			],
		},
		leave: {
			title: 'Leave Management',
			url: '/user/leave-module/',
			icon: TreePalm,
			isActive: false,
			secondaryMenus: [
				{ icon: Home, title: 'Home', url: '#', items: [] },
				{ icon: List, title: 'List', url: '#', items: [] },
				{ icon: ClipboardList, title: 'Report', url: '#', items: [] },
				{ icon: Settings, title: 'Claim Settings', url: '#', items: [] },
			],
		},
		attendance: {
			title: 'Attendance',
			url: '/user/attendance-module/',
			icon: CalendarDays,
			isActive: false,
			secondaryMenus: [
				{
					icon: Home,
					title: 'Overview',
					url: '#',
					items: [
						{
							title: 'Attendance Dashboard',
							url: '/user/attendance-module/',
						},
						{
							title: 'Project Logs',
							url: '/user/attendance-module/project-logs/',
						},
					],
				},
				{
					icon: Settings,
					title: 'Settings',
					url: '#',
					items: [
						{
							title: 'Attendance Settings',
							url: '/user/attendance-module/settings/',
						},
					],
				},
			],
		},
		expense_claim: {
			title: 'Expense Claim',
			url: '/user/expense-module/',
			icon: HandCoins,
			isActive: false,
			secondaryMenus: [
				{ icon: Home, title: 'Home', url: '#', items: [] },
				{ icon: List, title: 'List', url: '#', items: [] },
				{ icon: ClipboardList, title: 'Report', url: '#', items: [] },
				{ icon: Settings, title: 'Claim Settings', url: '#', items: [] },
			],
		},
		payroll: {
			title: 'Payroll',
			url: '/user/payroll-module/',
			icon: Banknote,
			isActive: false,
			secondaryMenus: [
				{ icon: Home, title: 'Home', url: '#', items: [] },
				{ icon: List, title: 'List', url: '#', items: [] },
				{ icon: ShieldCheck, title: 'Compliance', url: '#', items: [] },
				{ icon: ClipboardList, title: 'Report', url: '#', items: [] },
				{ icon: Landmark, title: 'Bank', url: '#', items: [] },
				{ icon: Settings, title: 'Settings', url: '#', items: [] },
			],
		},
		communication: {
			title: 'Communication',
			url: '/user/communication-module/',
			icon: Megaphone,
			isActive: false,
			secondaryMenus: [
				{ icon: Home, title: 'Home', url: '#', items: [] },
				{ icon: FileCheck, title: 'Submissions', url: '#', items: [] },
				{ icon: Settings, title: 'Settings', url: '#', items: [] },
			],
		},
		performance_and_appraisals: {
			title: 'Performance and Appraisals',
			url: '/user/performance-appraisals-module/',
			icon: Sparkles,
			isActive: false,
		},
	};

	return modules.map((module) => allModules[module]);
};

export function UserAppSidebar({ ...props }) {
	// Note: I'm using state to show active item.
	// IRL you should use the url/router.
	const {
		authenticatedUser: { role, isModuleAdmin, moduleAdminAccess },
	} = useAppSelector((store) => store.auth);
	const data = getData(role, isModuleAdmin, moduleAdminAccess);
	console.log(data);
	const [activeItem, setActiveItem] = React.useState(data.clientNav[0]);
	const [showChat, setShowChat] = React.useState(false); // New state for chat visibility
	const { setOpen } = useSidebar();

	return (
		<Sidebar
			variant="inset"
			collapsible="icon"
			className="overflow-hidden [&>[data-sidebar=sidebar]]:flex-row"
			{...props}
		>
			{/* This is the first sidebar */}
			{/* We disable collapsible and adjust width to icon. */}
			{/* This will make the sidebar appear as icons. */}
			<Sidebar
				variant="inset"
				collapsible="none"
				className="!w-[calc(var(--sidebar-width-icon)_+_1px)] mr-4"
			>
				<SidebarHeader>
					<SidebarMenu>
						<SidebarMenuItem>
							<SidebarMenuButton size="lg" asChild className="md:h-8 md:p-0">
								<Link href="/user">
									<div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-transparent text-sidebar-primary-foreground">
										<Image
											src={logo}
											width={64}
											height={64}
											className="object-fit size-6"
											alt="Harp HR"
										/>
									</div>
									<div className="grid flex-1 text-left text-sm leading-tight">
										<span className="truncate font-semibold">Valluva</span>
									</div>
								</Link>
							</SidebarMenuButton>
						</SidebarMenuItem>
					</SidebarMenu>
				</SidebarHeader>
				<SidebarContent>
					<SidebarGroup>
						<SidebarGroupContent className="px-1.5 md:px-0">
							<SidebarMenu>
								{data.clientNav.map((item) => (
									<SidebarMenuItem key={item.title}>
										<Link href={item.url}>
											<SidebarMenuButton
												tooltip={{
													children: item.title,
													hidden: false,
												}}
												onClick={() => {
													setActiveItem(item);
													setShowChat(item.title === 'Chat'); // Enable chat view when "Chat" is clicked
													setOpen(true);
												}}
												isActive={activeItem.title === item.title}
											>
												<item.icon />
												<span>{item.title}</span>
											</SidebarMenuButton>
										</Link>
									</SidebarMenuItem>
								))}
							</SidebarMenu>
						</SidebarGroupContent>
					</SidebarGroup>
				</SidebarContent>
				<SidebarFooter>
					<NavUser user={data.user} />
				</SidebarFooter>
			</Sidebar>
			{/* This is the second sidebar */}
			{/* We disable collapsible and let it fill remaining space */}
			<Sidebar
				variant="inset"
				collapsible="none"
				className="hidden flex-1 md:flex shadow-lg rounded-xl"
			>
				<SidebarHeader className="gap-3.5 max-h-16 p-4">
					<div className="flex w-full items-center justify-between">
						<div className="text-base font-medium text-foreground">
							{activeItem.title}
						</div>
						<Label className="flex items-center gap-2 text-sm">
							<activeItem.icon />
						</Label>
					</div>
				</SidebarHeader>
				<SidebarGroup className="px-0">
					{showChat ? (
						<ChatsList />
					) : (
						<SidebarMenu>
							{activeItem.secondaryMenus?.map((item) => (
								<Collapsible
									key={item.title}
									asChild
									defaultOpen
									className="group/collapsible"
								>
									<SidebarMenuItem>
										<CollapsibleTrigger asChild>
											<SidebarMenuButton tooltip={item.title}>
												{item.icon && <item.icon />}
												<span>{item.title}</span>
												<ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
											</SidebarMenuButton>
										</CollapsibleTrigger>
										<CollapsibleContent>
											<SidebarMenuSub>
												{item.items?.map((subItem) => (
													<SidebarMenuSubItem key={subItem.title}>
														<SidebarMenuSubButton asChild>
															<Link href={subItem.url}>
																<span>{subItem.title}</span>
															</Link>
														</SidebarMenuSubButton>
													</SidebarMenuSubItem>
												))}
											</SidebarMenuSub>
										</CollapsibleContent>
									</SidebarMenuItem>
								</Collapsible>
							))}
						</SidebarMenu>
					)}
				</SidebarGroup>
			</Sidebar>
		</Sidebar>
	);
}
