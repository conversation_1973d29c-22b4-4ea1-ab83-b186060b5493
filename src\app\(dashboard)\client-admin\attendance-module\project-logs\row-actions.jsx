'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
	MoreHorizontal,
	Eye,
	Edit,
	FileText,
	Mail,
	Phone,
	Download,
} from 'lucide-react';
import { toast } from 'sonner';
import { getAttendanceUserProjectLogs } from '@/lib/features/attendance/attendanceSlice';

export function DataTableRowActions({ row, dispatch }) {
	const router = useRouter();
	const employee = row.original;
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);

	const handleViewDetails = () => {
		// Navigate to employee details page using the [id] route
		router.push(
			`/client-admin/attendance-module/project-logs/${employee.userId}`
		);
	};

	const handleEditEmployee = () => {
		// TODO: Implement edit functionality
		toast.success('Edit functionality coming soon');
	};

	const handleGenerateReport = () => {
		// TODO: Implement report generation
		toast.success(`Generating report for ${employee.name}...`);
	};

	const handleSendEmail = () => {
		// Open email client with employee email
		window.location.href = `mailto:${employee.email}`;
	};

	const handleCallEmployee = () => {
		// Open phone dialer with employee phone
		window.location.href = `tel:${employee.phone}`;
	};

	const handleExportData = () => {
		// TODO: Implement data export
		toast.success(`Exporting data for ${employee.name}...`);
	};

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="ghost" className="h-8 w-8 p-0">
						<span className="sr-only">Open menu</span>
						<MoreHorizontal className="h-4 w-4" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					<DropdownMenuLabel>Actions</DropdownMenuLabel>
					<DropdownMenuItem onClick={handleViewDetails}>
						<Eye className="mr-2 h-4 w-4" />
						View Details
					</DropdownMenuItem>
					{/* <DropdownMenuItem onClick={handleEditEmployee}>
						<Edit className="mr-2 h-4 w-4" />
						Edit Employee
					</DropdownMenuItem>
					<DropdownMenuSeparator />
					<DropdownMenuItem onClick={handleGenerateReport}>
						<FileText className="mr-2 h-4 w-4" />
						Generate Report
					</DropdownMenuItem>
					<DropdownMenuItem onClick={handleExportData}>
						<Download className="mr-2 h-4 w-4" />
						Export Data
					</DropdownMenuItem>
					<DropdownMenuSeparator />
					<DropdownMenuItem onClick={handleSendEmail}>
						<Mail className="mr-2 h-4 w-4" />
						Send Email
					</DropdownMenuItem>
					<DropdownMenuItem onClick={handleCallEmployee}>
						<Phone className="mr-2 h-4 w-4" />
						Call Employee
					</DropdownMenuItem> */}
				</DropdownMenuContent>
			</DropdownMenu>

			<AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
						<AlertDialogDescription>
							This action cannot be undone. This will permanently delete{' '}
							{employee.name}'s data from our servers.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={() => {
								// TODO: Implement delete logic
								toast.success(`${employee.name} has been deleted.`);
								setShowDeleteDialog(false);
							}}
						>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</>
	);
}
