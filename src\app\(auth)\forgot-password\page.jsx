'use client';
import Image from 'next/image';
import logo from '@/assets/harp.png';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import SignUpVerifyEmailForm from '@/components/sign-up-form';
import VerifyOTPForm from '@/components/verify-otp-form';
import CompleteRegistrationForm from '@/components/complete-registeration-form';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { DotBackground } from '@/components/ui/dot-background';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
	forgotEmailSchema,
	registerEmailSchema,
} from '@/lib/schemas/authenticationSchema';
import { cn } from '@/lib/utils';
import { requestResetLink } from '@/lib/features/auth/authSlice';

export default function ForgotPasswordPage() {
	const router = useRouter();
	const dispatch = useAppDispatch();
	const { isLoading } = useAppSelector((store) => store.auth);

	const form = useForm({
		resolver: zodResolver(forgotEmailSchema),
		defaultValues: {
			email: '',
		},
		mode: 'onChange',
	});

	const onSubmit = async (data) => {
		console.log(data);
		const result = await dispatch(requestResetLink(data));

		if (requestResetLink.fulfilled.match(result)) {
			setTimeout(() => {
				router.push('/login');
			}, 3000);
		}
	};

	return (
		<div className="w-full max-w-sm">
			<Form {...form}>
				<form
					id="request-reset-link-form"
					className="flex flex-col gap-6"
					onSubmit={form.handleSubmit(onSubmit)}
				>
					<div className="flex flex-col items-center gap-2 text-center">
						<h1 className="text-2xl font-bold">Forgot your Password?</h1>
						<p className="text-balance text-sm text-muted-foreground">
							Enter your email below to request rest link
						</p>
					</div>
					<div className="grid gap-6">
						<FormField
							control={form.control}
							name="email"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Email</FormLabel>
									<FormControl>
										<Input
											{...field}
											type="email"
											autoComplete="email"
											autoFocus
											placeholder="Email"
										/>
									</FormControl>
									<FormDescription>Enter your company email</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<Button
							form="request-reset-link-form"
							type="submit"
							// onClick={form.handleSubmit(onSubmit)}
							className="w-full"
							disabled={isLoading}
						>
							{isLoading ? (
								<>
									<Loader2 className="animate-spin inline-block mr-2" />
									Requesting...
								</>
							) : (
								'Request Reset Link'
							)}
						</Button>
					</div>
					<div className="text-center text-sm">
						Back to{' '}
						<Link href="/login" className="underline underline-offset-4">
							Login
						</Link>
					</div>
				</form>
			</Form>
		</div>
	);
}
