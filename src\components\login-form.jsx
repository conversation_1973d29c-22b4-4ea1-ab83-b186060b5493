'use client';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from './ui/form';
import Link from 'next/link';
import { loginSchema } from '@/lib/schemas/authenticationSchema';
import { loginUser } from '@/lib/features/auth/authSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { Eye, EyeOff, Loader2 } from 'lucide-react';
import { useState } from 'react';
import { LoadingSubmitButton } from './loading-component';

export function LoginForm({ className, ...props }) {
	const [showPassword, setShowPassword] = useState(false);

	const form = useForm({
		mode: 'onChange',

		resolver: zodResolver(loginSchema),
		defaultValues: {
			email: '',
			password: '',
		},
	});
	const { isLoading } = useAppSelector((store) => store.auth);
	const dispatch = useAppDispatch();
	const onSubmit = (data) => {
		// console.log(`onSubmit - data:`, data);
		dispatch(loginUser(data));
	};
	return (
		<Form {...form}>
			<form
				className={cn('flex flex-col gap-6', className)}
				{...props}
				onSubmit={form.handleSubmit(onSubmit)}
			>
				<div className="flex flex-col items-center gap-2 text-center">
					<h1 className="text-2xl font-bold">Login to your account</h1>
					<p className="text-balance text-sm text-muted-foreground">
						Enter your email below to login to your account
					</p>
				</div>
				<div className="grid gap-6">
					<FormField
						control={form.control}
						name="email"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Email</FormLabel>
								<FormControl>
									<Input
										{...field}
										type="email"
										autoComplete="email"
										autoFocus
										placeholder="Email"
									/>
								</FormControl>
								<FormDescription>Enter your company email</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={form.control}
						name="password"
						render={({ field }) => (
							<FormItem>
								<div className="flex items-center">
									<FormLabel>Password</FormLabel>

									<Link
										href="forgot-password"
										className="ml-auto text-sm underline-offset-4 hover:underline"
									>
										Forgot your password?
									</Link>
								</div>
								<FormControl>
									<div className="relative w-full">
										<Input
											{...field}
											placeholder="Password"
											autoComplete="current-password"
											type={showPassword ? 'text' : 'password'}
										/>
										<Button
											type="button"
											variant="icon"
											onClick={() => setShowPassword((prev) => !prev)}
											className="absolute inset-y-0 right-0 px-3 flex items-center text-gray-500"
										>
											{showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
										</Button>
									</div>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<LoadingSubmitButton
						isLoading={isLoading}
						buttonLoadingText={'Logging In...'}
						buttonText={'Login'}
					/>
				</div>
				<div className="text-center text-sm">
					Don&apos;t have an account?{' '}
					<Link href="/sign-up" className="underline underline-offset-4">
						Sign up
					</Link>
				</div>
			</form>
		</Form>
	);
}
