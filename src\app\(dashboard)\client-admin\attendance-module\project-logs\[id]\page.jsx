'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import {
	ArrowLeft,
	Mail,
	Phone,
	Calendar,
	ChevronLeft,
	ChevronRight,
} from 'lucide-react';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	getAttendanceUserProjectCalender,
	getAttendanceUserProjectLogs,
} from '@/lib/features/attendance/attendanceSlice';
import { SimpleLoader } from '@/components/loading-component';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
dayjs.extend(duration);

// Helper function to convert minutes to hours
const convertMinutesToHours = (minutes) => {
	return dayjs.duration(minutes, 'minutes').format('HH:mm');
};

// Helper function to transform calendar data to daily hours format
const transformCalendarData = (calendarData) => {
	if (!calendarData || !Array.isArray(calendarData)) return {};

	const dailyHours = {};

	calendarData.forEach((monthData) => {
		const monthKey = monthData.month; // e.g., "2025-07"
		dailyHours[monthKey] = {};

		monthData.days.forEach((dayData) => {
			if (dayData.projectStats && dayData.projectStats.workingHours > 0) {
				const date = new Date(dayData.createdAt);
				const day = date.getDate();
				const hours = dayData.projectStats.workingHours;
				dailyHours[monthKey][day] = hours;
			}
		});
	});

	return dailyHours;
};

// Helper function to get days in month
const getDaysInMonth = (year, month) => {
	return new Date(year, month, 0).getDate();
};

// Helper function to get month name
const getMonthName = (monthKey) => {
	const [year, month] = monthKey.split('-');
	const date = new Date(year, month - 1);
	return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
};

// Daily Hours Table Component
const DailyHoursTable = ({ project, selectedMonth, onMonthChange }) => {
	const availableMonths = Object.keys(project.dailyHours || {});
	const currentMonthData = project.dailyHours?.[selectedMonth] || {};

	if (availableMonths.length === 0) {
		return (
			<div className="text-center py-4 text-muted-foreground">
				No daily hours data available for this project
			</div>
		);
	}

	const [year, month] = selectedMonth.split('-');
	const daysInMonth = getDaysInMonth(parseInt(year), parseInt(month));
	const days = Array.from({ length: daysInMonth }, (_, i) => i + 1);

	// Split days into weeks for better display
	const weeks = [];
	let currentWeek = [];
	const firstDayOfMonth = new Date(
		parseInt(year),
		parseInt(month) - 1,
		1
	).getDay();

	// Add empty cells for days before the first day of the month
	for (let i = 0; i < firstDayOfMonth; i++) {
		currentWeek.push(null);
	}

	days.forEach((day) => {
		currentWeek.push(day);
		if (currentWeek.length === 7) {
			weeks.push(currentWeek);
			currentWeek = [];
		}
	});

	// Add the last week if it has days
	if (currentWeek.length > 0) {
		// Fill the rest of the week with null
		while (currentWeek.length < 7) {
			currentWeek.push(null);
		}
		weeks.push(currentWeek);
	}

	const totalHours = Object.values(currentMonthData).reduce(
		(sum, hours) => sum + hours,
		0
	);

	// Get hours intensity for color coding
	const getHoursIntensity = (hours) => {
		if (!hours) return 'none';
		if (hours <= 3) return 'low';
		if (hours <= 5) return 'medium';
		if (hours <= 7) return 'high';
		return 'very-high';
	};

	// Get day type (weekend, today, etc.)
	const getDayType = (day) => {
		if (!day) return '';
		const [year, month] = selectedMonth.split('-');
		const date = new Date(parseInt(year), parseInt(month) - 1, day);
		const today = new Date();
		const dayOfWeek = date.getDay();

		if (date.toDateString() === today.toDateString()) return 'today';
		if (dayOfWeek === 0 || dayOfWeek === 6) return 'weekend';
		return 'weekday';
	};

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-3">
					<div className="flex items-center gap-2">
						<div className="w-3 h-3 rounded-full bg-gradient-to-r from-primary to-primary/70"></div>
						<h4 className="font-semibold text-base text-foreground">
							{project.name}
						</h4>
					</div>
					<Badge
						variant="secondary"
						className="text-xs font-medium px-3 py-1 bg-gradient-to-r from-primary/10 to-primary/5 text-primary border-primary/20"
					>
						{convertMinutesToHours(totalHours)}h this month
					</Badge>
				</div>
				<Select value={selectedMonth} onValueChange={onMonthChange}>
					<SelectTrigger className="w-44 bg-gradient-to-r from-background to-muted/30 border-2 border-primary/20 hover:border-primary/40 transition-colors">
						<SelectValue />
					</SelectTrigger>
					<SelectContent>
						{availableMonths.map((month) => (
							<SelectItem key={month} value={month}>
								{getMonthName(month)}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
			</div>

			{/* Legend */}
			<div className="flex items-center gap-6 text-xs text-muted-foreground">
				<div className="flex items-center gap-2">
					<span className="font-medium">Hours:</span>
					<div className="flex items-center gap-1">
						<div className="w-3 h-3 rounded bg-green-100 border border-green-200"></div>
						<span>1-3h</span>
					</div>
					<div className="flex items-center gap-1">
						<div className="w-3 h-3 rounded bg-yellow-100 border border-yellow-200"></div>
						<span>4-5h</span>
					</div>
					<div className="flex items-center gap-1">
						<div className="w-3 h-3 rounded bg-orange-100 border border-orange-200"></div>
						<span>6-7h</span>
					</div>
					<div className="flex items-center gap-1">
						<div className="w-3 h-3 rounded bg-red-100 border border-red-200"></div>
						<span>8+h</span>
					</div>
				</div>
			</div>

			<div className="border-2 border-primary/10 rounded-xl overflow-hidden bg-gradient-to-br from-background via-background to-muted/20 shadow-lg">
				<div className="bg-gradient-to-r from-primary/5 to-primary/10 border-b border-primary/20">
					<div className="grid grid-cols-7 gap-0">
						{[
							'Sunday',
							'Monday',
							'Tuesday',
							'Wednesday',
							'Thursday',
							'Friday',
							'Saturday',
						].map((dayName, index) => (
							<div
								key={dayName}
								className={`text-center py-3 font-semibold text-sm border-r border-primary/10 last:border-r-0 ${
									index === 0 || index === 6
										? 'text-primary/70'
										: 'text-foreground'
								}`}
							>
								{dayName.slice(0, 3)}
							</div>
						))}
					</div>
				</div>
				<div className="grid grid-cols-7 gap-0">
					{weeks.flat().map((day, index) => {
						const hours = day ? currentMonthData[day] : null;
						const intensity = getHoursIntensity(hours);
						const dayType = getDayType(day);

						return (
							<div
								key={index}
								className={`
									relative h-16 border-r border-b border-primary/10 last:border-r-0
									flex flex-col items-center justify-center transition-all duration-200									
									${dayType === 'today' ? 'ring-2 ring-primary ring-inset' : ''}
									${dayType === 'weekend' ? 'bg-muted/30' : 'bg-background'}
									${intensity === 'low' ? 'bg-gradient-to-br from-green-50 to-green-100 border-green-200' : ''}
									${intensity === 'medium' ? 'bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200' : ''}
									${intensity === 'high' ? 'bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200' : ''}
									${intensity === 'very-high' ? 'bg-gradient-to-br from-red-50 to-red-100 border-red-200' : ''}
								`}
							>
								{day && (
									<>
										<span
											className={`text-sm font-semibold mb-1 ${
												dayType === 'today'
													? 'text-primary'
													: dayType === 'weekend'
														? 'text-muted-foreground'
														: 'text-foreground'
											}`}
										>
											{day}
										</span>
										{hours && (
											<div
												className={`
												px-2 py-0.5 rounded-full text-xs font-bold shadow-sm
												${intensity === 'low' ? 'bg-green-200 text-green-800' : ''}
												${intensity === 'medium' ? 'bg-yellow-200 text-yellow-800' : ''}
												${intensity === 'high' ? 'bg-orange-200 text-orange-800' : ''}
												${intensity === 'very-high' ? 'bg-red-200 text-red-800' : ''}
											`}
											>
												{hours}h
											</div>
										)}
										{dayType === 'today' && (
											<div className="absolute top-1 right-1 w-2 h-2 bg-primary rounded-full animate-pulse"></div>
										)}
									</>
								)}
							</div>
						);
					})}
				</div>
			</div>

			{/* Summary Stats */}
			<div className="grid grid-cols-4 gap-3">
				<div className="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-3 text-center">
					<div className="text-lg font-bold text-green-800">
						{Object.values(currentMonthData).filter((h) => h <= 3).length}
					</div>
					<div className="text-xs text-green-600">Light Days</div>
				</div>
				<div className="bg-gradient-to-br from-yellow-50 to-yellow-100 border border-yellow-200 rounded-lg p-3 text-center">
					<div className="text-lg font-bold text-yellow-800">
						{
							Object.values(currentMonthData).filter((h) => h > 3 && h <= 5)
								.length
						}
					</div>
					<div className="text-xs text-yellow-600">Medium Days</div>
				</div>
				<div className="bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-lg p-3 text-center">
					<div className="text-lg font-bold text-orange-800">
						{
							Object.values(currentMonthData).filter((h) => h > 5 && h <= 7)
								.length
						}
					</div>
					<div className="text-xs text-orange-600">Normal Days</div>
				</div>
				<div className="bg-gradient-to-br from-red-50 to-red-100 border border-red-200 rounded-lg p-3 text-center">
					<div className="text-lg font-bold text-red-800">
						{Object.values(currentMonthData).filter((h) => h > 7).length}
					</div>
					<div className="text-xs text-red-600">Heavy Days</div>
				</div>
			</div>
		</div>
	);
};

export default function EmployeeDetailsPage() {
	const { id: userId } = useParams();
	const {
		userProjectDetails: employee,
		isLoading,
		userProjectCalender: calenderData,
		isLoadingProject,
	} = useAppSelector((store) => store.attendance);
	const dispatch = useAppDispatch();
	const router = useRouter();
	const [selectedMonths, setSelectedMonths] = useState({});
	const [selectedProject, setSelectedProject] = useState(0);

	const transformedEmployee = employee
		? {
				...employee,
				totalHours: employee.totalWorkingMinutes,
				projectCount: employee.projects?.length || 0,
				avgHoursPerWeek: employee.totalWorkingMinutes / 4,
				projects:
					employee.projects?.map((project) => ({
						...project,
						name: project.projectName,
						hours: project.totalWorkingMinutes,
						dailyHours: transformCalendarData(calenderData),
					})) || [],
				position: employee.designation,
				phone:
					`${employee.countryDialCode || ''} ${employee.mobile || ''}`.trim(),
				joinDate: employee.joinedAt,
				location: employee.country,
				lastActive: new Date().toISOString(),
			}
		: null;

	useEffect(() => {
		if (!employee) {
			dispatch(getAttendanceUserProjectLogs(userId));
		}
	}, [dispatch, userId, employee]);

	const currentProject = transformedEmployee?.projects[0] || null;

	useEffect(() => {
		if (currentProject?.projectId) {
			dispatch(
				getAttendanceUserProjectCalender({
					userId,
					projectId: currentProject.projectId,
				})
			);
		}
	}, [dispatch, userId, selectedProject]);

	const handleMonthChange = (projectIndex, month) => {
		setSelectedMonths((prev) => ({
			...prev,
			[projectIndex]: month,
		}));
	};

	if (isLoading) {
		return <SimpleLoader />;
	}

	if (!transformedEmployee) {
		return (
			<div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
				<h2 className="text-2xl font-semibold">Employee Not Found</h2>
				<p className="text-muted-foreground">
					The employee you're looking for doesn't exist.
				</p>
				<Button onClick={() => router.back()}>
					<ArrowLeft className="mr-2 h-4 w-4" />
					Go Back
				</Button>
			</div>
		);
	}

	return (
		<div className="w-full h-full p-4 space-y-6">
			{/* Header with Employee Details */}
			<div className="relative">
				<div className="flex items-start gap-6">
					<div className="relative">
						<Avatar className="h-20 w-20 ring-4 ring-primary/10 shadow-lg">
							<AvatarImage src={transformedEmployee?.profilePhoto} />
							<AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-2xl">
								{transformedEmployee?.name
									?.split(' ')
									?.map((n) => n[0])
									?.join('')}
							</AvatarFallback>
						</Avatar>
						<div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 border-2 border-background rounded-full flex items-center justify-center">
							<div className="w-2 h-2 bg-white rounded-full"></div>
						</div>
					</div>
					<div className="flex-1">
						<div className="flex items-start justify-between mb-4">
							<div>
								<h1 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
									{transformedEmployee.name}
								</h1>
								<p className="text-xl text-muted-foreground font-medium">
									{transformedEmployee.position}
								</p>
							</div>
							<div className="flex gap-2">
								<Badge
									variant="outline"
									className="text-sm px-3 py-1 font-medium border-primary/20 text-primary"
								>
									{transformedEmployee.department}
								</Badge>
							</div>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
							<div className="space-y-2">
								<h3 className="font-semibold text-sm text-primary flex items-center gap-2">
									<div className="w-2 h-2 bg-primary rounded-full"></div>
									Contact Information
								</h3>
								<div className="space-y-1 pl-4">
									<p className="text-sm font-medium">
										{transformedEmployee.email}
									</p>
									<p className="text-sm font-medium">
										{transformedEmployee.phone}
									</p>
								</div>
							</div>
							<div className="space-y-2">
								<h3 className="font-semibold text-sm text-secondary flex items-center gap-2">
									<div className="w-2 h-2 bg-secondary rounded-full"></div>
									Work Details
								</h3>
								<div className="space-y-1 pl-4">
									<p className="text-sm font-medium">
										<span className="text-muted-foreground">Joined:</span>{' '}
										{new Date(
											transformedEmployee.joinDate
										).toLocaleDateString()}
									</p>
									<p className="text-sm font-medium">
										<span className="text-muted-foreground">Location:</span>{' '}
										{transformedEmployee.location}
									</p>
								</div>
							</div>
							<div className="space-y-2">
								<h3 className="font-semibold text-sm text-accent flex items-center gap-2">
									<div className="w-2 h-2 bg-accent rounded-full"></div>
									Activity
								</h3>
								<div className="space-y-1 pl-4">
									<p className="text-sm font-medium">
										<span className="text-muted-foreground">Last Active:</span>{' '}
										{new Date(
											transformedEmployee.lastActive
										).toLocaleDateString()}
									</p>
									<p className="text-sm font-medium">
										<span className="text-muted-foreground">Projects:</span>{' '}
										{transformedEmployee.projectCount}
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Performance Overview - Split Layout */}
			<div className="grid grid-cols-1 gap-6">
				{/* Left Section - Summary Cards */}
				<div className="grid grid-cols-3 gap-4">
					<div className="relative bg-gradient-to-br from-primary/5 to-primary/10 border border-primary/20 rounded-xl p-5 text-center flex flex-col items-center justify-center">
						<div className="absolute top-3 right-3 w-2 h-2 bg-primary rounded-full opacity-60"></div>
						<div className="text-3xl font-bold text-primary mb-1">
							{convertMinutesToHours(transformedEmployee.totalHours)}h
						</div>
						<p className="text-sm font-medium text-primary/70">Total Hours</p>
					</div>
					<div className="relative bg-gradient-to-br from-secondary/5 to-secondary/10 border border-secondary/20 rounded-xl p-5 text-center flex flex-col items-center justify-center">
						<div className="absolute top-3 right-3 w-2 h-2 bg-secondary rounded-full opacity-60"></div>
						<div className="text-3xl font-bold text-secondary mb-1">
							{transformedEmployee.projectCount}
						</div>
						<p className="text-sm font-medium text-secondary/70">Projects</p>
					</div>
					<div className="relative bg-gradient-to-br from-accent/5 to-accent/10 border border-accent/20 rounded-xl p-5 text-center flex flex-col items-center justify-center">
						<div className="absolute top-3 right-3 w-2 h-2 bg-accent rounded-full opacity-60"></div>
						<div className="text-3xl font-bold text-accent mb-1">
							{convertMinutesToHours(transformedEmployee.avgHoursPerWeek)}h
						</div>
						<p className="text-sm font-medium text-accent/70">Avg/Week</p>
					</div>
				</div>
			</div>

			{/* Daily Hours Calendar - Single Project View */}
			<Card>
				<CardHeader>
					<CardTitle>Daily Hours Calendar</CardTitle>
					<CardDescription>
						View daily work hours for each project by month
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="flex items-center gap-4 mb-6">
						<div className="flex items-center gap-2">
							<label className="text-sm font-medium">Project:</label>
							<Select
								value={selectedProject.toString()}
								onValueChange={(value) => setSelectedProject(parseInt(value))}
							>
								<SelectTrigger className="w-64">
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									{transformedEmployee?.projects?.map((project, index) => (
										<SelectItem key={index} value={index.toString()}>
											{project.name} ({convertMinutesToHours(project.hours)}h
											total)
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
					</div>

					{isLoadingProject && (
						<div className="flex items-center justify-center py-8">
							<SimpleLoader />
						</div>
					)}

					{!isLoadingProject &&
						transformedEmployee?.projects?.length > 0 &&
						transformedEmployee?.projects[selectedProject] && (
							<DailyHoursTable
								project={transformedEmployee?.projects[selectedProject]}
								selectedMonth={
									selectedMonths[selectedProject] ||
									Object.keys(
										transformedEmployee?.projects[selectedProject].dailyHours ||
											{}
									)[0]
								}
								onMonthChange={(month) =>
									handleMonthChange(selectedProject, month)
								}
							/>
						)}
				</CardContent>
			</Card>
		</div>
	);
}
