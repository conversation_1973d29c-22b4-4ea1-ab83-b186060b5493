'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useFieldArray, useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { PlusCircle, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { experienceDetailsFormSchema } from '@/lib/schemas/employeeRegistrationSchema';

export function EmployeeExperienceDetailsForm() {
	const [uploadedFiles, setUploadedFiles] = useState({});

	const form = useForm({
		resolver: zodResolver(experienceDetailsFormSchema),
		mode: 'onChange',

		defaultValues: {
			employeeOrgId: '',
			experienceDetails: [
				{
					location: '',
					companyName: '',
					designation: '',
					periodFrom: '',
					periodTo: '',
					reasonForLeaving: '',
					// document: '',
				},
			],
		},
	});

	const { fields, append, remove } = useFieldArray({
		name: 'experienceDetails',
		control: form.control,
	});

	function onSubmit(data) {
		const formData = new FormData();

		// Add non-file data
		formData.append('employeeOrgId', data.employeeOrgId);

		// Add experience details and files
		data.experienceDetails.forEach((detail, index) => {
			Object.entries(detail).forEach(([key, value]) => {
				if (key !== 'document') {
					formData.append(`experienceDetails[${index}][${key}]`, value);
				}
			});

			// Add file if it exists
			const file = uploadedFiles[index];
			if (file) {
				formData.append(`experienceDetails[${index}][document]`, file);
			}
		});

		// Log FormData contents
		for (const [key, value] of formData.entries()) {
			console.log(key, value);
		}
	}

	const handleFileChange = (index, e) => {
		const file = e.target.files?.[0] || null;
		setUploadedFiles((prev) => ({ ...prev, [index]: file }));
	};

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
				<FormField
					control={form.control}
					name="employeeOrgId"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Employee ID</FormLabel>
							<FormControl>
								<Input placeholder="Enter employee ID" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<div className="space-y-4">
					<h3 className="text-lg font-medium">Experience Details</h3>
					<Separator />
					{fields.map((field, index) => (
						<Card key={field.id} className="relative">
							<CardContent className="pt-6">
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
									<FormField
										control={form.control}
										name={`experienceDetails.${index}.location`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Location</FormLabel>
												<FormControl>
													<Input placeholder="Enter location" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`experienceDetails.${index}.companyName`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Company Name</FormLabel>
												<FormControl>
													<Input placeholder="Enter company name" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`experienceDetails.${index}.designation`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Designation</FormLabel>
												<FormControl>
													<Input placeholder="Enter designation" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`experienceDetails.${index}.periodFrom`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Period From</FormLabel>
												<FormControl>
													<Input type="date" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`experienceDetails.${index}.periodTo`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Period To</FormLabel>
												<FormControl>
													<Input type="date" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`experienceDetails.${index}.document`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Document</FormLabel>
												<FormControl>
													<Input
														type="file"
														accept="image/*,.pdf"
														onChange={(e) => {
															handleFileChange(index, e);
															field.onChange(e.target.files?.[0]?.name || '');
														}}
													/>
												</FormControl>
												<FormDescription>
													Upload image or PDF document
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`experienceDetails.${index}.reasonForLeaving`}
										render={({ field }) => (
											<FormItem className="md:col-span-2  lg:col-span-3">
												<FormLabel>Reason for Leaving</FormLabel>
												<FormControl>
													<Textarea
														placeholder="Enter reason for leaving"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
								{index > 0 && (
									<Button
										type="button"
										variant="ghost"
										size="icon"
										onClick={() => remove(index)}
										className="absolute top-2 right-2"
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								)}
							</CardContent>
						</Card>
					))}
					<Button
						type="button"
						variant="outline"
						size="sm"
						onClick={() =>
							append({
								location: '',
								companyName: '',
								designation: '',
								periodFrom: '',
								periodTo: '',
								reasonForLeaving: '',
								document: '',
							})
						}
					>
						<PlusCircle className="mr-2 h-4 w-4" />
						Add More Experience
					</Button>
				</div>

				<div className="flex justify-end space-x-4">
					<Button type="button" variant="outline">
						Cancel
					</Button>
					<Button type="submit">Save Details</Button>
				</div>
			</form>
		</Form>
	);
}
