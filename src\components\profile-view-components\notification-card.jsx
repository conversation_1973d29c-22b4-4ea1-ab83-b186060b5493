'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { MessageCircle, Clock } from 'lucide-react';
import { getInitials, userRoles } from '@/lib/utils';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	getMessagesForHappenings,
	setSelectedChat,
} from '@/lib/features/chat/chatSlice';
import { useRouter } from 'next/navigation';

export function NotificationCard() {
	const { happenings: messages, isLoading: loading } = useAppSelector(
		(store) => store.chat
	);
	const { authenticatedUser } = useAppSelector((store) => store.auth);
	const dispatch = useAppDispatch();
	const router = useRouter();
	// Function to format the date
	const formatDate = (dateString) => {
		const date = new Date(dateString);
		const now = new Date();
		const diffInMinutes = Math.floor((now - date) / (1000 * 60));

		if (diffInMinutes < 1) return 'Just now';
		if (diffInMinutes < 60) return `${diffInMinutes} mins ago`;

		const diffInHours = Math.floor(diffInMinutes / 60);
		if (diffInHours < 24) return `${diffInHours} hours ago`;

		const diffInDays = Math.floor(diffInHours / 24);
		if (diffInDays < 7) return `${diffInDays} days ago`;

		return date.toLocaleDateString();
	};

	// Fetch data from API
	useEffect(() => {
		dispatch(getMessagesForHappenings());
	}, [dispatch]);

	const navigateToChat = (chatId) => {
		const role =
			authenticatedUser?.role > userRoles.CLIENT_ADMIN
				? 'user'
				: 'client-admin';
		router.push(`/${role}/chat`);
		dispatch(setSelectedChat(chatId));
	};

	// Loading skeleton
	const LoadingSkeleton = () => (
		<div className="space-y-3">
			{[1, 2, 3].map((i) => (
				<div key={i} className="flex items-start space-x-3 p-3">
					<Skeleton className="h-10 w-10 rounded-full" />
					<div className="space-y-2 flex-1">
						<Skeleton className="h-4 w-24" />
						<Skeleton className="h-3 w-full" />
					</div>
				</div>
			))}
		</div>
	);

	return (
		<Card className="w-full h-[350px] flex flex-col">
			<CardHeader className="flex-shrink-0 pb-3">
				<div className="flex items-center justify-between">
					<CardTitle className="flex items-center gap-2">
						<MessageCircle className="h-5 w-5" />
						Happenings
					</CardTitle>
					{messages.length > 0 && (
						<Badge variant="secondary" className="text-xs">
							{messages.length} messages
						</Badge>
					)}
				</div>
			</CardHeader>
			<CardContent className="flex-1 overflow-hidden p-0">
				<ScrollArea className="h-full px-4">
					{loading ? (
						<LoadingSkeleton />
					) : messages.length === 0 ? (
						<div className="flex flex-col items-center justify-center h-full text-center p-4">
							<MessageCircle className="h-12 w-12 text-muted-foreground mb-2" />
							<p className="text-muted-foreground">No messages yet</p>
						</div>
					) : (
						<div className="space-y-1">
							{messages.map((msg) => (
								<Alert
									className="border-0 py-3 px-0 bg-transparent hover:bg-muted/50 transition-colors rounded-lg"
									key={msg._id}
								>
									<div className="flex items-start space-x-3 w-full">
										<Avatar className="h-10 w-10 flex-shrink-0">
											<AvatarImage
												src={msg.from.profilePhoto || '/placeholder.svg'}
												alt={msg.from.name}
											/>
											<AvatarFallback className="text-xs">
												{getInitials(msg.from.name)}
											</AvatarFallback>
										</Avatar>
										<div className="flex-1 min-w-0">
											<AlertTitle className="font-semibold text-sm mb-1 flex items-center gap-2">
												{msg.from.name}
												<div className="flex items-center text-muted-foreground text-xs">
													<Clock className="h-3 w-3 mr-1" />
													{formatDate(msg.createdAt)}
												</div>
											</AlertTitle>
											<AlertDescription className="flex justify-between items-start gap-2">
												<span className="text-sm leading-relaxed break-words">
													{msg.message}
												</span>
												<Button
													size="sm"
													variant="outline"
													className="text-xs px-3 py-1 flex-shrink-0 h-7 bg-transparent"
													onClick={() => {
														navigateToChat(msg._id);
													}}
												>
													Reply
												</Button>
											</AlertDescription>
										</div>
									</div>
								</Alert>
							))}
						</div>
					)}
				</ScrollArea>
			</CardContent>
		</Card>
	);
}
